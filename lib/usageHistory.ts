/**
 * Usage History Tracking System for Disc Golf Inventory Management System
 *
 * This module provides comprehensive tracking of disc usage events including:
 * - Disc views and interactions
 * - Edit operations and field changes
 * - Location changes and movements
 * - Usage patterns and analytics
 *
 * Features:
 * - Type-safe event tracking with Zod validation
 * - localStorage persistence with error handling
 * - Event aggregation and analytics
 * - Privacy-focused local-only tracking
 * - Automatic cleanup of old events
 */

import { z } from "zod";
import { LocalStorageService, StorageResult, StorageError, StorageErrorType } from "./storage";
import { Location, DiscCondition } from "./types";
import type { Disc } from "./types";

// ============================================================================
// TYPES & SCHEMAS
// ============================================================================

/**
 * Types of usage events that can be tracked
 */
export enum UsageEventType {
  VIEW = "view",
  EDIT = "edit",
  LOCATION_CHANGE = "location_change",
  CONDITION_CHANGE = "condition_change",
  NOTE_UPDATE = "note_update",
  IMAGE_UPDATE = "image_update",
  CREATED = "created",
  DELETED = "deleted",
}

/**
 * Usage event metadata for different event types
 */
export interface UsageEventMetadata {
  // View events
  viewDuration?: number;
  referrer?: string;

  // Edit events
  fieldsChanged?: string[];
  oldValues?: Record<string, unknown>;
  newValues?: Record<string, unknown>;

  // Location changes
  oldLocation?: Location;
  newLocation?: Location;

  // Condition changes
  oldCondition?: DiscCondition;
  newCondition?: DiscCondition;

  // General metadata
  userAgent?: string;
  sessionId?: string;
  source?: "manual" | "import" | "bulk" | "api";
}

/**
 * Individual usage event
 */
export interface UsageEvent {
  id: string;
  discId: string;
  type: UsageEventType;
  timestamp: Date;
  metadata?: UsageEventMetadata;
}

/**
 * Usage statistics for a specific disc
 */
export interface DiscUsageStats {
  discId: string;
  totalViews: number;
  totalEdits: number;
  lastViewed?: Date;
  lastEdited?: Date;
  createdAt: Date;
  locationChanges: number;
  conditionChanges: number;
  averageViewDuration?: number;
  editFrequency: number; // edits per day since creation
}

/**
 * Collection-wide usage analytics
 */
export interface CollectionUsageAnalytics {
  totalEvents: number;
  totalViews: number;
  totalEdits: number;
  mostViewedDiscs: Array<{ discId: string; views: number }>;
  mostEditedDiscs: Array<{ discId: string; edits: number }>;
  recentActivity: UsageEvent[];
  dailyActivity: Record<string, number>;
  eventTypeDistribution: Record<UsageEventType, number>;
}

// ============================================================================
// VALIDATION SCHEMAS
// ============================================================================

/**
 * Zod schema for usage event validation
 */
export const UsageEventSchema = z.object({
  id: z.string().uuid(),
  discId: z.string().uuid(),
  type: z.nativeEnum(UsageEventType),
  timestamp: z.date(),
  metadata: z
    .object({
      viewDuration: z.number().positive().optional(),
      referrer: z.string().optional(),
      fieldsChanged: z.array(z.string()).optional(),
      oldValues: z.record(z.string(), z.unknown()).optional(),
      newValues: z.record(z.string(), z.unknown()).optional(),
      oldLocation: z.nativeEnum(Location).optional(),
      newLocation: z.nativeEnum(Location).optional(),
      oldCondition: z.nativeEnum(DiscCondition).optional(),
      newCondition: z.nativeEnum(DiscCondition).optional(),
      userAgent: z.string().optional(),
      sessionId: z.string().optional(),
      source: z.enum(["manual", "import", "bulk", "api"]).optional(),
    })
    .optional(),
});

// ============================================================================
// CONSTANTS
// ============================================================================

/**
 * Storage key for usage history
 */
export const USAGE_HISTORY_STORAGE_KEY = "disc_golf_inventory_usage_history";

/**
 * Maximum number of events to store (for performance)
 */
export const MAX_USAGE_EVENTS = 10000;

/**
 * Maximum age of events in days (for cleanup)
 */
export const MAX_EVENT_AGE_DAYS = 365;

/**
 * Session storage key for current session ID
 */
export const SESSION_ID_KEY = "disc_golf_inventory_session_id";

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Generate a unique event ID
 */
function generateEventId(): string {
  return crypto.randomUUID();
}

/**
 * Generate or retrieve session ID
 */
function getSessionId(): string {
  if (typeof window === "undefined") {
    return "server-session";
  }

  let sessionId = sessionStorage.getItem(SESSION_ID_KEY);
  if (!sessionId) {
    sessionId = crypto.randomUUID();
    sessionStorage.setItem(SESSION_ID_KEY, sessionId);
  }
  return sessionId;
}

/**
 * Get current user agent (safely)
 */
function getUserAgent(): string | undefined {
  if (typeof navigator !== "undefined") {
    return navigator.userAgent;
  }
  return undefined;
}

/**
 * Create a new usage event
 */
export function createUsageEvent(discId: string, type: UsageEventType, metadata?: UsageEventMetadata): UsageEvent {
  return {
    id: generateEventId(),
    discId,
    type,
    timestamp: new Date(),
    metadata: {
      ...metadata,
      sessionId: getSessionId(),
      userAgent: getUserAgent(),
    },
  };
}

/**
 * Validate usage event data
 */
export function validateUsageEvent(event: unknown): StorageResult<UsageEvent> {
  try {
    const validatedEvent = UsageEventSchema.parse(event);
    return { success: true, data: validatedEvent };
  } catch (error) {
    const storageError = new StorageError(
      "VALIDATION_ERROR" as any,
      `Invalid usage event data: ${error instanceof Error ? error.message : "Unknown error"}`,
      error instanceof Error ? error : undefined
    );
    return { success: false, error: storageError };
  }
}

// ============================================================================
// STORAGE FUNCTIONS
// ============================================================================

/**
 * Get storage service instance
 */
const storage = LocalStorageService.getInstance();

/**
 * Load usage history from storage
 */
function loadUsageHistoryFromStorage(): UsageEvent[] {
  const result = storage.getItem<UsageEvent[]>(USAGE_HISTORY_STORAGE_KEY);
  if (!result.success || !result.data) {
    return [];
  }

  // Validate and filter events
  return result.data
    .map((event) => {
      const validation = validateUsageEvent(event);
      return validation.success ? validation.data! : null;
    })
    .filter((event): event is UsageEvent => event !== null);
}

/**
 * Save usage history to storage
 */
function saveUsageHistoryToStorage(events: UsageEvent[]): StorageResult<void> {
  return storage.setItem(USAGE_HISTORY_STORAGE_KEY, events);
}

/**
 * Clean up old events based on age and count limits
 */
function cleanupOldEvents(events: UsageEvent[]): UsageEvent[] {
  const now = new Date();
  const maxAge = MAX_EVENT_AGE_DAYS * 24 * 60 * 60 * 1000; // Convert to milliseconds

  // Filter by age
  const recentEvents = events.filter((event) => {
    const eventAge = now.getTime() - event.timestamp.getTime();
    return eventAge <= maxAge;
  });

  // Sort by timestamp (newest first) and limit count
  const sortedEvents = recentEvents.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());

  return sortedEvents.slice(0, MAX_USAGE_EVENTS);
}

// ============================================================================
// CORE TRACKING FUNCTIONS
// ============================================================================

/**
 * Record a usage event
 */
export function recordUsageEvent(
  discId: string,
  type: UsageEventType,
  metadata?: UsageEventMetadata
): StorageResult<UsageEvent> {
  try {
    // Create the event
    const event = createUsageEvent(discId, type, metadata);

    // Validate the event
    const validation = validateUsageEvent(event);
    if (!validation.success) {
      return validation;
    }

    // Load existing events
    const existingEvents = loadUsageHistoryFromStorage();

    // Add new event
    const updatedEvents = [event, ...existingEvents];

    // Clean up old events
    const cleanedEvents = cleanupOldEvents(updatedEvents);

    // Save to storage
    const saveResult = saveUsageHistoryToStorage(cleanedEvents);
    if (!saveResult.success) {
      return { success: false, error: saveResult.error };
    }

    return { success: true, data: event };
  } catch (error) {
    const storageError = new StorageError(
      "OPERATION_FAILED" as any,
      `Failed to record usage event: ${error instanceof Error ? error.message : "Unknown error"}`,
      error instanceof Error ? error : undefined
    );
    return { success: false, error: storageError };
  }
}

/**
 * Get usage history for a specific disc
 */
export function getDiscUsageHistory(discId: string): UsageEvent[] {
  const allEvents = loadUsageHistoryFromStorage();
  return allEvents
    .filter((event) => event.discId === discId)
    .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
}

/**
 * Get recent usage events across all discs
 */
export function getRecentUsageEvents(limit: number = 50): UsageEvent[] {
  const allEvents = loadUsageHistoryFromStorage();
  return allEvents.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime()).slice(0, limit);
}

// ============================================================================
// ANALYTICS & STATISTICS
// ============================================================================

/**
 * Calculate usage statistics for a specific disc
 */
export function calculateDiscUsageStats(discId: string): DiscUsageStats {
  const events = getDiscUsageHistory(discId);

  const viewEvents = events.filter((e) => e.type === UsageEventType.VIEW);
  const editEvents = events.filter((e) => e.type === UsageEventType.EDIT);
  const locationChangeEvents = events.filter((e) => e.type === UsageEventType.LOCATION_CHANGE);
  const conditionChangeEvents = events.filter((e) => e.type === UsageEventType.CONDITION_CHANGE);

  // Calculate average view duration
  const viewDurations = viewEvents
    .map((e) => e.metadata?.viewDuration)
    .filter((duration): duration is number => typeof duration === "number");
  const averageViewDuration =
    viewDurations.length > 0
      ? viewDurations.reduce((sum, duration) => sum + duration, 0) / viewDurations.length
      : undefined;

  // Find creation date
  const createdEvent = events.find((e) => e.type === UsageEventType.CREATED);
  const createdAt = createdEvent?.timestamp || new Date();

  // Calculate edit frequency (edits per day)
  const daysSinceCreation = Math.max(1, (Date.now() - createdAt.getTime()) / (1000 * 60 * 60 * 24));
  const editFrequency = editEvents.length / daysSinceCreation;

  return {
    discId,
    totalViews: viewEvents.length,
    totalEdits: editEvents.length,
    lastViewed: viewEvents[0]?.timestamp,
    lastEdited: editEvents[0]?.timestamp,
    createdAt,
    locationChanges: locationChangeEvents.length,
    conditionChanges: conditionChangeEvents.length,
    averageViewDuration,
    editFrequency,
  };
}

/**
 * Calculate collection-wide usage analytics
 */
export function calculateCollectionUsageAnalytics(): CollectionUsageAnalytics {
  const allEvents = loadUsageHistoryFromStorage();

  // Basic counts
  const totalEvents = allEvents.length;
  const totalViews = allEvents.filter((e) => e.type === UsageEventType.VIEW).length;
  const totalEdits = allEvents.filter((e) => e.type === UsageEventType.EDIT).length;

  // Most viewed/edited discs
  const discViewCounts = new Map<string, number>();
  const discEditCounts = new Map<string, number>();

  allEvents.forEach((event) => {
    if (event.type === UsageEventType.VIEW) {
      discViewCounts.set(event.discId, (discViewCounts.get(event.discId) || 0) + 1);
    } else if (event.type === UsageEventType.EDIT) {
      discEditCounts.set(event.discId, (discEditCounts.get(event.discId) || 0) + 1);
    }
  });

  const mostViewedDiscs = Array.from(discViewCounts.entries())
    .map(([discId, views]) => ({ discId, views }))
    .sort((a, b) => b.views - a.views)
    .slice(0, 10);

  const mostEditedDiscs = Array.from(discEditCounts.entries())
    .map(([discId, edits]) => ({ discId, edits }))
    .sort((a, b) => b.edits - a.edits)
    .slice(0, 10);

  // Recent activity
  const recentActivity = getRecentUsageEvents(20);

  // Daily activity (last 30 days)
  const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
  const recentEvents = allEvents.filter((e) => e.timestamp >= thirtyDaysAgo);

  const dailyActivity: Record<string, number> = {};
  recentEvents.forEach((event) => {
    const dateKey = event.timestamp.toISOString().split("T")[0];
    dailyActivity[dateKey] = (dailyActivity[dateKey] || 0) + 1;
  });

  // Event type distribution
  const eventTypeDistribution: Record<UsageEventType, number> = {
    [UsageEventType.VIEW]: 0,
    [UsageEventType.EDIT]: 0,
    [UsageEventType.LOCATION_CHANGE]: 0,
    [UsageEventType.CONDITION_CHANGE]: 0,
    [UsageEventType.NOTE_UPDATE]: 0,
    [UsageEventType.IMAGE_UPDATE]: 0,
    [UsageEventType.CREATED]: 0,
    [UsageEventType.DELETED]: 0,
  };

  allEvents.forEach((event) => {
    eventTypeDistribution[event.type]++;
  });

  return {
    totalEvents,
    totalViews,
    totalEdits,
    mostViewedDiscs,
    mostEditedDiscs,
    recentActivity,
    dailyActivity,
    eventTypeDistribution,
  };
}

/**
 * Clear all usage history (for privacy/reset purposes)
 */
export function clearUsageHistory(): StorageResult<void> {
  return storage.removeItem(USAGE_HISTORY_STORAGE_KEY);
}

/**
 * Export usage history as JSON
 */
export function exportUsageHistory(): string {
  const events = loadUsageHistoryFromStorage();
  return JSON.stringify(events, null, 2);
}
