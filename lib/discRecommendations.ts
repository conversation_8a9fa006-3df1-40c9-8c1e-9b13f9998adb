/**
 * Disc Recommendations System for Disc Golf Inventory Management System
 *
 * This module provides intelligent disc recommendations based on:
 * - Flight number similarity
 * - Manufacturer and mold relationships
 * - Bag composition analysis
 * - Usage patterns and preferences
 * - Disc type and stability matching
 *
 * Features:
 * - Multiple recommendation algorithms
 * - Weighted scoring system
 * - Configurable recommendation parameters
 * - Type-safe recommendation results
 */

import { DiscCondition, Location } from "./types";
import type { Disc, FlightNumbers } from "./types";
import { DiscType, StabilityType } from "./discUtils";

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

/**
 * Recommendation reason types
 */
export enum RecommendationReason {
  SIMILAR_FLIGHT = "similar_flight",
  SAME_MANUFACTURER = "same_manufacturer",
  SAME_MOLD = "same_mold",
  COMPLEMENTARY_FLIGHT = "complementary_flight",
  BAG_GAP_FILLER = "bag_gap_filler",
  SIMILAR_USAGE = "similar_usage",
  CONDITION_UPGRADE = "condition_upgrade",
  BACKUP_DISC = "backup_disc",
}

/**
 * Individual disc recommendation
 */
export interface DiscRecommendation {
  disc: Disc;
  score: number;
  reasons: RecommendationReason[];
  explanation: string;
  similarity: number; // 0-1 scale
}

/**
 * Recommendation configuration
 */
export interface RecommendationConfig {
  maxRecommendations: number;
  includeOwnedDiscs: boolean;
  weightFlightSimilarity: number;
  weightManufacturerMatch: number;
  weightMoldMatch: number;
  weightComplementaryFlight: number;
  weightBagGaps: number;
  weightUsagePatterns: number;
  minScore: number;
}

/**
 * Flight number similarity weights
 */
interface FlightSimilarityWeights {
  speed: number;
  glide: number;
  turn: number;
  fade: number;
}

// ============================================================================
// CONSTANTS
// ============================================================================

/**
 * Default recommendation configuration
 */
export const DEFAULT_RECOMMENDATION_CONFIG: RecommendationConfig = {
  maxRecommendations: 6,
  includeOwnedDiscs: false,
  weightFlightSimilarity: 0.4,
  weightManufacturerMatch: 0.15,
  weightMoldMatch: 0.1,
  weightComplementaryFlight: 0.2,
  weightBagGaps: 0.1,
  weightUsagePatterns: 0.05,
  minScore: 0.1,
};

/**
 * Flight number similarity weights (how important each flight number is for similarity)
 */
const FLIGHT_SIMILARITY_WEIGHTS: FlightSimilarityWeights = {
  speed: 0.3,
  glide: 0.2,
  turn: 0.3,
  fade: 0.2,
};

/**
 * Manufacturer relationships (brands that make similar discs)
 */
const MANUFACTURER_RELATIONSHIPS: Record<string, string[]> = {
  Innova: ["Discmania", "Infinite Discs"],
  Discraft: ["Ledgestone Insurance", "Brodie Smith"],
  "Dynamic Discs": ["Latitude 64", "Westside Discs"],
  "Latitude 64": ["Dynamic Discs", "Westside Discs"],
  "Westside Discs": ["Dynamic Discs", "Latitude 64"],
  "MVP Disc Sports": ["Axiom Discs", "Streamline Discs"],
  "Axiom Discs": ["MVP Disc Sports", "Streamline Discs"],
  "Streamline Discs": ["MVP Disc Sports", "Axiom Discs"],
};

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Calculate flight number similarity between two discs
 */
function calculateFlightSimilarity(flight1: FlightNumbers, flight2: FlightNumbers): number {
  const speedDiff = Math.abs(flight1.speed - flight2.speed) / 14; // Max speed is 14
  const glideDiff = Math.abs(flight1.glide - flight2.glide) / 7; // Max glide is 7
  const turnDiff = Math.abs(flight1.turn - flight2.turn) / 6; // Turn range is -5 to 1
  const fadeDiff = Math.abs(flight1.fade - flight2.fade) / 5; // Max fade is 5

  const weightedDiff =
    speedDiff * FLIGHT_SIMILARITY_WEIGHTS.speed +
    glideDiff * FLIGHT_SIMILARITY_WEIGHTS.glide +
    turnDiff * FLIGHT_SIMILARITY_WEIGHTS.turn +
    fadeDiff * FLIGHT_SIMILARITY_WEIGHTS.fade;

  return Math.max(0, 1 - weightedDiff);
}

/**
 * Check if two discs are complementary (fill different flight patterns)
 */
function areComplementaryFlights(flight1: FlightNumbers, flight2: FlightNumbers): number {
  const stability1 = getStabilityType(flight1);
  const stability2 = getStabilityType(flight2);
  const type1 = getDiscType(flight1);
  const type2 = getDiscType(flight2);

  // Same type but different stability is complementary
  if (type1 === type2 && stability1 !== stability2) {
    return 0.8;
  }

  // Different types in same speed range is somewhat complementary
  if (Math.abs(flight1.speed - flight2.speed) <= 2 && type1 !== type2) {
    return 0.6;
  }

  // Different speed ranges for coverage
  const speedDiff = Math.abs(flight1.speed - flight2.speed);
  if (speedDiff >= 4) {
    return 0.4;
  }

  return 0;
}

/**
 * Calculate manufacturer relationship score
 */
function calculateManufacturerScore(manufacturer1: string, manufacturer2: string): number {
  if (manufacturer1 === manufacturer2) {
    return 1.0;
  }

  const relationships = MANUFACTURER_RELATIONSHIPS[manufacturer1] || [];
  if (relationships.includes(manufacturer2)) {
    return 0.7;
  }

  return 0;
}

/**
 * Analyze bag gaps for a collection
 */
function analyzeBagGaps(collection: Disc[]): {
  missingTypes: DiscType[];
  missingStabilities: StabilityType[];
  speedGaps: number[];
} {
  const types = new Set<DiscType>();
  const stabilities = new Set<StabilityType>();
  const speeds = new Set<number>();

  collection.forEach((disc) => {
    types.add(getDiscType(disc.flightNumbers));
    stabilities.add(getStabilityType(disc.flightNumbers));
    speeds.add(disc.flightNumbers.speed);
  });

  // Find missing types
  const allTypes: DiscType[] = [DiscType.PUTTER, DiscType.MIDRANGE, DiscType.FAIRWAY_DRIVER, DiscType.DISTANCE_DRIVER];
  const missingTypes = allTypes.filter((type) => !types.has(type));

  // Find missing stabilities
  const allStabilities: StabilityType[] = [StabilityType.OVERSTABLE, StabilityType.STABLE, StabilityType.UNDERSTABLE];
  const missingStabilities = allStabilities.filter((stability) => !stabilities.has(stability));

  // Find speed gaps (missing speeds in ranges)
  const speedArray = Array.from(speeds).sort((a, b) => a - b);
  const speedGaps: number[] = [];
  for (let i = 1; i < speedArray.length; i++) {
    const gap = speedArray[i] - speedArray[i - 1];
    if (gap > 2) {
      for (let speed = speedArray[i - 1] + 1; speed < speedArray[i]; speed++) {
        speedGaps.push(speed);
      }
    }
  }

  return { missingTypes, missingStabilities, speedGaps };
}

/**
 * Calculate how well a disc fills bag gaps
 */
function calculateBagGapScore(disc: Disc, bagGaps: ReturnType<typeof analyzeBagGaps>): number {
  let score = 0;

  const discType = getDiscType(disc.flightNumbers);
  const discStability = getStabilityType(disc.flightNumbers);
  const discSpeed = disc.flightNumbers.speed;

  // Check if disc fills missing type
  if (bagGaps.missingTypes.includes(discType)) {
    score += 0.5;
  }

  // Check if disc fills missing stability
  if (bagGaps.missingStabilities.includes(discStability)) {
    score += 0.3;
  }

  // Check if disc fills speed gap
  if (bagGaps.speedGaps.includes(discSpeed)) {
    score += 0.2;
  }

  return Math.min(1, score);
}

// ============================================================================
// MAIN RECOMMENDATION FUNCTIONS
// ============================================================================

/**
 * Generate disc recommendations for a given disc
 */
export function generateDiscRecommendations(
  targetDisc: Disc,
  collection: Disc[],
  allDiscs: Disc[],
  config: RecommendationConfig = DEFAULT_RECOMMENDATION_CONFIG
): DiscRecommendation[] {
  const recommendations: DiscRecommendation[] = [];
  const bagGaps = analyzeBagGaps(collection);

  // Filter discs to consider
  const candidateDiscs = config.includeOwnedDiscs
    ? allDiscs
    : allDiscs.filter((disc) => !collection.some((owned) => owned.id === disc.id));

  // Exclude the target disc itself
  const filteredCandidates = candidateDiscs.filter((disc) => disc.id !== targetDisc.id);

  for (const candidate of filteredCandidates) {
    const recommendation = calculateDiscRecommendation(targetDisc, candidate, bagGaps, config);

    if (recommendation.score >= config.minScore) {
      recommendations.push(recommendation);
    }
  }

  // Sort by score and limit results
  return recommendations.sort((a, b) => b.score - a.score).slice(0, config.maxRecommendations);
}

/**
 * Calculate recommendation score for a single disc
 */
function calculateDiscRecommendation(
  targetDisc: Disc,
  candidate: Disc,
  bagGaps: ReturnType<typeof analyzeBagGaps>,
  config: RecommendationConfig
): DiscRecommendation {
  const reasons: RecommendationReason[] = [];
  let score = 0;
  let explanation = "";

  // Flight similarity score
  const flightSimilarity = calculateFlightSimilarity(targetDisc.flightNumbers, candidate.flightNumbers);
  if (flightSimilarity > 0.7) {
    reasons.push(RecommendationReason.SIMILAR_FLIGHT);
    score += flightSimilarity * config.weightFlightSimilarity;
  }

  // Manufacturer match
  const manufacturerScore = calculateManufacturerScore(targetDisc.manufacturer, candidate.manufacturer);
  if (manufacturerScore > 0) {
    if (manufacturerScore === 1.0) {
      reasons.push(RecommendationReason.SAME_MANUFACTURER);
    }
    score += manufacturerScore * config.weightManufacturerMatch;
  }

  // Mold match
  if (targetDisc.mold === candidate.mold) {
    reasons.push(RecommendationReason.SAME_MOLD);
    score += config.weightMoldMatch;
  }

  // Complementary flight
  const complementaryScore = areComplementaryFlights(targetDisc.flightNumbers, candidate.flightNumbers);
  if (complementaryScore > 0.5) {
    reasons.push(RecommendationReason.COMPLEMENTARY_FLIGHT);
    score += complementaryScore * config.weightComplementaryFlight;
  }

  // Bag gap filling
  const bagGapScore = calculateBagGapScore(candidate, bagGaps);
  if (bagGapScore > 0.3) {
    reasons.push(RecommendationReason.BAG_GAP_FILLER);
    score += bagGapScore * config.weightBagGaps;
  }

  // Condition upgrade opportunity
  if (
    targetDisc.mold === candidate.mold &&
    targetDisc.manufacturer === candidate.manufacturer &&
    candidate.condition === DiscCondition.NEW &&
    targetDisc.condition !== DiscCondition.NEW
  ) {
    reasons.push(RecommendationReason.CONDITION_UPGRADE);
    score += 0.2;
  }

  // Generate explanation
  explanation = generateRecommendationExplanation(targetDisc, candidate, reasons, flightSimilarity);

  return {
    disc: candidate,
    score: Math.min(1, score),
    reasons,
    explanation,
    similarity: flightSimilarity,
  };
}

/**
 * Generate human-readable explanation for recommendation
 */
function generateRecommendationExplanation(
  targetDisc: Disc,
  candidate: Disc,
  reasons: RecommendationReason[],
  similarity: number
): string {
  const explanations: string[] = [];

  if (reasons.includes(RecommendationReason.SIMILAR_FLIGHT)) {
    explanations.push(`Similar flight pattern (${Math.round(similarity * 100)}% match)`);
  }

  if (reasons.includes(RecommendationReason.SAME_MANUFACTURER)) {
    explanations.push(`Same manufacturer (${candidate.manufacturer})`);
  }

  if (reasons.includes(RecommendationReason.SAME_MOLD)) {
    explanations.push(`Same mold in different plastic`);
  }

  if (reasons.includes(RecommendationReason.COMPLEMENTARY_FLIGHT)) {
    explanations.push("Complements your current disc's flight");
  }

  if (reasons.includes(RecommendationReason.BAG_GAP_FILLER)) {
    explanations.push("Fills a gap in your bag");
  }

  if (reasons.includes(RecommendationReason.CONDITION_UPGRADE)) {
    explanations.push("Newer condition of disc you already own");
  }

  return explanations.join(", ") || "Recommended based on your collection";
}
