/**
 * Breadcrumb Navigation Component for Disc Golf Inventory Management System
 *
 * A breadcrumb navigation component that provides hierarchical navigation
 * with proper accessibility, keyboard support, and responsive design.
 */

"use client";

import * as React from "react";
import Link from "next/link";
import { ChevronRight, Home } from "lucide-react";
import { cn } from "@/lib/utils";

// ============================================================================
// TYPES
// ============================================================================

export interface BreadcrumbItem {
  label: string;
  href?: string;
  current?: boolean;
}

interface BreadcrumbProps {
  items: BreadcrumbItem[];
  className?: string;
  separator?: React.ReactNode;
  showHome?: boolean;
  homeHref?: string;
}

// ============================================================================
// COMPONENT
// ============================================================================

/**
 * Breadcrumb Navigation Component
 *
 * Provides hierarchical navigation with proper ARIA attributes and
 * keyboard navigation support. Automatically handles current page
 * styling and accessibility.
 *
 * @param props - Component props
 * @returns JSX element
 */
export function Breadcrumb({
  items,
  className,
  separator = <ChevronRight className="h-4 w-4" />,
  showHome = true,
  homeHref = "/",
}: BreadcrumbProps) {
  // Combine home item with provided items
  const allItems = React.useMemo(() => {
    const breadcrumbItems = [...items];
    
    if (showHome) {
      breadcrumbItems.unshift({
        label: "Home",
        href: homeHref,
        current: false,
      });
    }
    
    return breadcrumbItems;
  }, [items, showHome, homeHref]);

  return (
    <nav
      aria-label="Breadcrumb navigation"
      className={cn("flex items-center space-x-1 text-sm text-muted-foreground", className)}
    >
      <ol className="flex items-center space-x-1">
        {allItems.map((item, index) => {
          const isLast = index === allItems.length - 1;
          const isCurrent = item.current || isLast;

          return (
            <li key={index} className="flex items-center space-x-1">
              {/* Separator (not for first item) */}
              {index > 0 && (
                <span className="text-muted-foreground/50" aria-hidden="true">
                  {separator}
                </span>
              )}

              {/* Breadcrumb item */}
              {item.href && !isCurrent ? (
                <Link
                  href={item.href}
                  className={cn(
                    "hover:text-foreground transition-colors",
                    "focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 rounded-sm px-1 py-0.5",
                    index === 0 && showHome && "flex items-center gap-1"
                  )}
                  aria-current={isCurrent ? "page" : undefined}
                >
                  {index === 0 && showHome && <Home className="h-3 w-3" />}
                  <span>{item.label}</span>
                </Link>
              ) : (
                <span
                  className={cn(
                    "font-medium",
                    isCurrent ? "text-foreground" : "text-muted-foreground",
                    index === 0 && showHome && "flex items-center gap-1"
                  )}
                  aria-current={isCurrent ? "page" : undefined}
                >
                  {index === 0 && showHome && <Home className="h-3 w-3" />}
                  <span>{item.label}</span>
                </span>
              )}
            </li>
          );
        })}
      </ol>
    </nav>
  );
}

// ============================================================================
// UTILITY HOOKS
// ============================================================================

/**
 * Hook to generate breadcrumb items from pathname
 */
export function useBreadcrumbFromPath(pathname: string): BreadcrumbItem[] {
  return React.useMemo(() => {
    const segments = pathname.split("/").filter(Boolean);
    const items: BreadcrumbItem[] = [];

    let currentPath = "";
    
    segments.forEach((segment, index) => {
      currentPath += `/${segment}`;
      const isLast = index === segments.length - 1;
      
      // Format segment for display
      let label = segment
        .split("-")
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(" ");

      // Special cases for known routes
      if (segment === "inventory") {
        label = "Inventory";
      } else if (segment === "add") {
        label = "Add Disc";
      } else if (segment === "stats") {
        label = "Statistics";
      } else if (segment === "settings") {
        label = "Settings";
      } else if (segment === "import") {
        label = "Import";
      } else if (segment === "export") {
        label = "Export";
      }

      items.push({
        label,
        href: isLast ? undefined : currentPath,
        current: isLast,
      });
    });

    return items;
  }, [pathname]);
}

/**
 * Hook to generate breadcrumb items for disc detail pages
 */
export function useDiscDetailBreadcrumb(discName?: string): BreadcrumbItem[] {
  return React.useMemo(() => {
    const items: BreadcrumbItem[] = [
      {
        label: "Inventory",
        href: "/inventory",
      },
    ];

    if (discName) {
      items.push({
        label: discName,
        current: true,
      });
    } else {
      items.push({
        label: "Disc Details",
        current: true,
      });
    }

    return items;
  }, [discName]);
}
