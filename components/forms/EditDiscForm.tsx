/**
 * EditDiscForm Component for Disc Golf Inventory Management System
 *
 * A form component for editing existing discs with pre-populated values,
 * real-time validation, and inline editing capabilities.
 *
 * Features:
 * - Pre-populated form fields from existing disc data
 * - Real-time validation with Zod schemas
 * - Inline editing with save/cancel actions
 * - Change tracking and dirty state management
 * - Accessible form design with proper ARIA attributes
 */

"use client";

import * as React from "react";
import { useForm, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { FormField, FormFieldGroup } from "./FormField";
import { UpdateDiscSchema } from "@/lib/validation";
import { DiscCondition, Location, FLIGHT_NUMBER_RANGES, DISC_WEIGHT_RANGE, type Disc } from "@/lib/types";
import { DISC_MANUFACTURERS, getPlasticTypesForManufacturer, DISC_COLORS } from "@/lib/discData";
import { formatConditionText, formatLocationText } from "@/lib/discUtils";
import { cn } from "@/lib/utils";
import { Save, X, Loader2, AlertCircle } from "lucide-react";
import type { UpdateDiscInput } from "@/hooks/useInventory";

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

/**
 * Form data type based on UpdateDiscInput
 */
type EditDiscFormData = Omit<UpdateDiscInput, "updatedAt">;

/**
 * EditDiscForm component props
 */
interface EditDiscFormProps {
  /** The disc to edit */
  disc: Disc;
  /** Callback when form is successfully saved */
  onSave: (disc: UpdateDiscInput) => void;
  /** Callback when editing is cancelled */
  onCancel: () => void;
  /** Additional CSS classes */
  className?: string;
  /** Whether to show the form in a card wrapper */
  showCard?: boolean;
}

// ============================================================================
// COMPONENT
// ============================================================================

/**
 * EditDiscForm component with real-time validation and change tracking
 *
 * @param props - EditDiscForm component props
 * @returns JSX element
 */
export function EditDiscForm({ disc, onSave, onCancel, className, showCard = true }: EditDiscFormProps) {
  const [isSubmitting, setIsSubmitting] = React.useState(false);
  const [submitError, setSubmitError] = React.useState<string | null>(null);

  // Form setup with React Hook Form and Zod validation
  const {
    control,
    handleSubmit,
    watch,
    setValue,
    reset,
    formState: { errors, isValid, isDirty },
  } = useForm<EditDiscFormData>({
    resolver: zodResolver(UpdateDiscSchema.omit({ updatedAt: true })),
    mode: "onChange",
    defaultValues: {
      id: disc.id,
      manufacturer: disc.manufacturer,
      mold: disc.mold,
      plasticType: disc.plasticType,
      weight: disc.weight,
      condition: disc.condition,
      flightNumbers: disc.flightNumbers,
      color: disc.color,
      notes: disc.notes || "",
      currentLocation: disc.currentLocation,
      imageUrl: disc.imageUrl || "",
      purchaseDate: disc.purchaseDate,
      purchasePrice: disc.purchasePrice,
    },
  });

  // Watch manufacturer for plastic type filtering
  const selectedManufacturer = watch("manufacturer");
  const availablePlasticTypes = React.useMemo(() => {
    return selectedManufacturer ? getPlasticTypesForManufacturer(selectedManufacturer) : [];
  }, [selectedManufacturer]);

  // Form submission handler
  const handleFormSubmit = async (data: EditDiscFormData) => {
    try {
      setIsSubmitting(true);
      setSubmitError(null);

      // Create update data with timestamp
      const updateData: UpdateDiscInput = {
        ...data,
        updatedAt: new Date(),
      };

      onSave(updateData);
    } catch (error) {
      setSubmitError(error instanceof Error ? error.message : "An unexpected error occurred");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle cancel
  const handleCancel = () => {
    reset(); // Reset form to original values
    setSubmitError(null);
    onCancel();
  };

  const formContent = (
    <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6" role="form">
      {/* Error display */}
      {submitError && (
        <div className="flex items-center gap-2 p-3 text-sm text-destructive bg-destructive/10 border border-destructive/20 rounded-md">
          <AlertCircle className="h-4 w-4" />
          <span>{submitError}</span>
        </div>
      )}

      {/* Basic Information */}
      <FormFieldGroup title="Basic Information" description="Update the basic details about your disc">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Manufacturer */}
          <Controller
            name="manufacturer"
            control={control}
            render={({ field }) => (
              <div className="space-y-2">
                <label htmlFor="manufacturer" className="text-sm font-medium">
                  Manufacturer *
                </label>
                <Select value={field.value} onValueChange={field.onChange}>
                  <SelectTrigger className={cn(errors.manufacturer && "border-destructive")}>
                    <SelectValue placeholder="Select manufacturer" />
                  </SelectTrigger>
                  <SelectContent>
                    {DISC_MANUFACTURERS.map((manufacturer) => (
                      <SelectItem key={manufacturer} value={manufacturer}>
                        {manufacturer}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.manufacturer && (
                  <div className="flex items-center gap-2 text-sm text-destructive">
                    <AlertCircle className="h-4 w-4" />
                    <span>{errors.manufacturer.message}</span>
                  </div>
                )}
              </div>
            )}
          />

          {/* Mold */}
          <FormField
            label="Mold"
            name="mold"
            control={control}
            error={errors.mold}
            placeholder="e.g., Destroyer, Buzzz, Judge"
            required
          />

          {/* Plastic Type */}
          <Controller
            name="plasticType"
            control={control}
            render={({ field }) => (
              <div className="space-y-2">
                <label htmlFor="plasticType" className="text-sm font-medium">
                  Plastic Type *
                </label>
                <Select value={field.value} onValueChange={field.onChange}>
                  <SelectTrigger className={cn(errors.plasticType && "border-destructive")}>
                    <SelectValue placeholder="Select plastic type" />
                  </SelectTrigger>
                  <SelectContent>
                    {availablePlasticTypes.map((plastic) => (
                      <SelectItem key={plastic} value={plastic}>
                        {plastic}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.plasticType && (
                  <div className="flex items-center gap-2 text-sm text-destructive">
                    <AlertCircle className="h-4 w-4" />
                    <span>{errors.plasticType.message}</span>
                  </div>
                )}
              </div>
            )}
          />

          {/* Weight */}
          <FormField
            label="Weight (grams)"
            name="weight"
            control={control}
            error={errors.weight}
            type="number"
            min={DISC_WEIGHT_RANGE.min}
            max={DISC_WEIGHT_RANGE.max}
            step="1"
            required
          />
        </div>
      </FormFieldGroup>

      {/* Flight Numbers */}
      <FormFieldGroup title="Flight Numbers" description="Enter the disc's flight characteristics">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <FormField
            label="Speed"
            name="flightNumbers.speed"
            control={control}
            error={errors.flightNumbers?.speed}
            type="number"
            min={FLIGHT_NUMBER_RANGES.speed.min}
            max={FLIGHT_NUMBER_RANGES.speed.max}
            step="1"
            required
          />
          <FormField
            label="Glide"
            name="flightNumbers.glide"
            control={control}
            error={errors.flightNumbers?.glide}
            type="number"
            min={FLIGHT_NUMBER_RANGES.glide.min}
            max={FLIGHT_NUMBER_RANGES.glide.max}
            step="1"
            required
          />
          <FormField
            label="Turn"
            name="flightNumbers.turn"
            control={control}
            error={errors.flightNumbers?.turn}
            type="number"
            min={FLIGHT_NUMBER_RANGES.turn.min}
            max={FLIGHT_NUMBER_RANGES.turn.max}
            step="1"
            required
          />
          <FormField
            label="Fade"
            name="flightNumbers.fade"
            control={control}
            error={errors.flightNumbers?.fade}
            type="number"
            min={FLIGHT_NUMBER_RANGES.fade.min}
            max={FLIGHT_NUMBER_RANGES.fade.max}
            step="1"
            required
          />
        </div>
      </FormFieldGroup>

      {/* Physical Properties */}
      <FormFieldGroup title="Physical Properties" description="Describe the disc's appearance and condition">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Color */}
          <Controller
            name="color"
            control={control}
            render={({ field }) => (
              <div className="space-y-2">
                <label htmlFor="color" className="text-sm font-medium">
                  Color *
                </label>
                <Select value={field.value} onValueChange={field.onChange}>
                  <SelectTrigger className={cn(errors.color && "border-destructive")}>
                    <SelectValue placeholder="Select color" />
                  </SelectTrigger>
                  <SelectContent>
                    {DISC_COLORS.map((color) => (
                      <SelectItem key={color} value={color}>
                        {color}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.color && (
                  <div className="flex items-center gap-2 text-sm text-destructive">
                    <AlertCircle className="h-4 w-4" />
                    <span>{errors.color.message}</span>
                  </div>
                )}
              </div>
            )}
          />

          {/* Condition */}
          <Controller
            name="condition"
            control={control}
            render={({ field }) => (
              <div className="space-y-2">
                <label htmlFor="condition" className="text-sm font-medium">
                  Condition *
                </label>
                <Select value={field.value} onValueChange={field.onChange}>
                  <SelectTrigger className={cn(errors.condition && "border-destructive")}>
                    <SelectValue placeholder="Select condition" />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.values(DiscCondition).map((condition) => (
                      <SelectItem key={condition} value={condition}>
                        {formatConditionText(condition)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.condition && (
                  <div className="flex items-center gap-2 text-sm text-destructive">
                    <AlertCircle className="h-4 w-4" />
                    <span>{errors.condition.message}</span>
                  </div>
                )}
              </div>
            )}
          />
        </div>
      </FormFieldGroup>

      {/* Form Actions */}
      <div className="flex items-center justify-end gap-3 pt-6 border-t">
        <Button type="button" variant="outline" onClick={handleCancel} disabled={isSubmitting}>
          <X className="h-4 w-4 mr-2" />
          Cancel
        </Button>
        <Button type="submit" disabled={!isValid || !isDirty || isSubmitting}>
          {isSubmitting ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Saving...
            </>
          ) : (
            <>
              <Save className="h-4 w-4 mr-2" />
              Save Changes
            </>
          )}
        </Button>
      </div>
    </form>
  );

  if (showCard) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>Edit Disc</CardTitle>
        </CardHeader>
        <CardContent>{formContent}</CardContent>
      </Card>
    );
  }

  return <div className={className}>{formContent}</div>;
}
