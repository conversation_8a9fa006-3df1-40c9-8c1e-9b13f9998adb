/**
 * Disc Detail Actions Component for Disc Golf Inventory Management System
 *
 * This component provides action buttons for the disc detail page,
 * including edit, save, cancel, share, and navigation actions.
 */

"use client";

import * as React from "react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  ArrowLeft,
  Edit,
  Save,
  X,
  Share,
  MoreHorizontal,
  Copy,
  ExternalLink,
  Trash2,
} from "lucide-react";
import type { Disc } from "@/lib/types";

// ============================================================================
// TYPES
// ============================================================================

interface DiscDetailActionsProps {
  disc: Disc;
  isEditing: boolean;
  onEdit: () => void;
  onSave: (disc: Disc) => void;
  onCancel: () => void;
  onShare: () => void;
  onBack: () => void;
  onDelete?: () => void;
}

// ============================================================================
// COMPONENT
// ============================================================================

/**
 * Disc Detail Actions Component
 *
 * Provides contextual action buttons based on the current state:
 * - View mode: Edit, Share, More options
 * - Edit mode: Save, Cancel
 * - Always: Back to inventory
 *
 * @param props - Component props
 * @returns JSX element
 */
export function DiscDetailActions({
  disc,
  isEditing,
  onEdit,
  onSave,
  onCancel,
  onShare,
  onBack,
  onDelete,
}: DiscDetailActionsProps) {
  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(window.location.href);
      // Could show a toast notification here
    } catch (error) {
      console.error("Failed to copy link:", error);
    }
  };

  const handleOpenInNewTab = () => {
    window.open(window.location.href, "_blank");
  };

  if (isEditing) {
    // Edit mode actions
    return (
      <div className="flex items-center gap-2">
        <Button onClick={onCancel} variant="outline" size="sm">
          <X className="h-4 w-4 mr-2" />
          Cancel
        </Button>
        <Button onClick={() => onSave(disc)} size="sm">
          <Save className="h-4 w-4 mr-2" />
          Save Changes
        </Button>
      </div>
    );
  }

  // View mode actions
  return (
    <div className="flex items-center gap-2">
      {/* Back button */}
      <Button onClick={onBack} variant="outline" size="sm">
        <ArrowLeft className="h-4 w-4 mr-2" />
        Back
      </Button>

      {/* Edit button */}
      <Button onClick={onEdit} variant="outline" size="sm">
        <Edit className="h-4 w-4 mr-2" />
        Edit
      </Button>

      {/* Share button */}
      <Button onClick={onShare} variant="outline" size="sm">
        <Share className="h-4 w-4 mr-2" />
        Share
      </Button>

      {/* More actions dropdown */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" size="sm">
            <MoreHorizontal className="h-4 w-4" />
            <span className="sr-only">More actions</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-48">
          <DropdownMenuItem onClick={handleCopyLink}>
            <Copy className="h-4 w-4 mr-2" />
            Copy Link
          </DropdownMenuItem>
          <DropdownMenuItem onClick={handleOpenInNewTab}>
            <ExternalLink className="h-4 w-4 mr-2" />
            Open in New Tab
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          {onDelete && (
            <DropdownMenuItem onClick={onDelete} className="text-destructive">
              <Trash2 className="h-4 w-4 mr-2" />
              Delete Disc
            </DropdownMenuItem>
          )}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}
