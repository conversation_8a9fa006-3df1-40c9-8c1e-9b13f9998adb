/**
 * Disc Usage History Component for Disc Golf Inventory Management System
 *
 * This component displays the usage history and statistics for a specific disc,
 * including views, edits, location changes, and other tracked events.
 *
 * Features:
 * - Chronological event timeline
 * - Usage statistics summary
 * - Event type filtering
 * - Relative time formatting
 * - Interactive event details
 */

"use client";

import * as React from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { formatRelativeTime, formatDateTime } from "@/lib/discUtils";
import { UsageEventType, type UsageEvent, type DiscUsageStats } from "@/lib/usageHistory";
import {
  Eye,
  Edit,
  MapPin,
  AlertCircle,
  Image,
  FileText,
  Plus,
  Trash2,
  Clock,
  TrendingUp,
  Filter,
} from "lucide-react";

// ============================================================================
// TYPES
// ============================================================================

interface DiscUsageHistoryProps {
  discId: string;
  usageHistory: UsageEvent[];
  usageStats: DiscUsageStats;
  maxEvents?: number;
}

// ============================================================================
// CONSTANTS
// ============================================================================

/**
 * Event type configurations for display
 */
const EVENT_TYPE_CONFIG = {
  [UsageEventType.VIEW]: {
    icon: Eye,
    label: "Viewed",
    color: "bg-blue-100 text-blue-800",
    description: "Disc was viewed",
  },
  [UsageEventType.EDIT]: {
    icon: Edit,
    label: "Edited",
    color: "bg-green-100 text-green-800",
    description: "Disc details were modified",
  },
  [UsageEventType.LOCATION_CHANGE]: {
    icon: MapPin,
    label: "Moved",
    color: "bg-purple-100 text-purple-800",
    description: "Location was changed",
  },
  [UsageEventType.CONDITION_CHANGE]: {
    icon: AlertCircle,
    label: "Condition Updated",
    color: "bg-orange-100 text-orange-800",
    description: "Condition was updated",
  },
  [UsageEventType.NOTE_UPDATE]: {
    icon: FileText,
    label: "Notes Updated",
    color: "bg-yellow-100 text-yellow-800",
    description: "Notes were modified",
  },
  [UsageEventType.IMAGE_UPDATE]: {
    icon: Image,
    label: "Image Updated",
    color: "bg-pink-100 text-pink-800",
    description: "Image was changed",
  },
  [UsageEventType.CREATED]: {
    icon: Plus,
    label: "Created",
    color: "bg-emerald-100 text-emerald-800",
    description: "Disc was added to collection",
  },
  [UsageEventType.DELETED]: {
    icon: Trash2,
    label: "Deleted",
    color: "bg-red-100 text-red-800",
    description: "Disc was removed from collection",
  },
};

// ============================================================================
// COMPONENT
// ============================================================================

/**
 * Disc Usage History Component
 *
 * Displays a timeline of usage events and statistics for a specific disc.
 * Provides insights into how the disc has been used and modified over time.
 *
 * @param props - Component props
 * @returns JSX element
 */
export function DiscUsageHistory({
  discId,
  usageHistory,
  usageStats,
  maxEvents = 20,
}: DiscUsageHistoryProps) {
  const [showAllEvents, setShowAllEvents] = React.useState(false);
  const [filterType, setFilterType] = React.useState<UsageEventType | null>(null);

  // Filter and limit events
  const filteredEvents = React.useMemo(() => {
    let events = usageHistory;
    
    if (filterType) {
      events = events.filter(event => event.type === filterType);
    }
    
    if (!showAllEvents) {
      events = events.slice(0, maxEvents);
    }
    
    return events;
  }, [usageHistory, filterType, showAllEvents, maxEvents]);

  // Get unique event types for filtering
  const availableEventTypes = React.useMemo(() => {
    const types = new Set(usageHistory.map(event => event.type));
    return Array.from(types);
  }, [usageHistory]);

  const handleFilterChange = (type: UsageEventType | null) => {
    setFilterType(type);
  };

  const formatEventDetails = (event: UsageEvent): string => {
    const config = EVENT_TYPE_CONFIG[event.type];
    let details = config.description;

    if (event.metadata) {
      switch (event.type) {
        case UsageEventType.VIEW:
          if (event.metadata.viewDuration) {
            const seconds = Math.round(event.metadata.viewDuration / 1000);
            details += ` for ${seconds} seconds`;
          }
          break;
        case UsageEventType.EDIT:
          if (event.metadata.fieldsChanged?.length) {
            details += ` (${event.metadata.fieldsChanged.join(", ")})`;
          }
          break;
        case UsageEventType.LOCATION_CHANGE:
          if (event.metadata.oldLocation && event.metadata.newLocation) {
            details += ` from ${event.metadata.oldLocation} to ${event.metadata.newLocation}`;
          }
          break;
        case UsageEventType.CONDITION_CHANGE:
          if (event.metadata.oldCondition && event.metadata.newCondition) {
            details += ` from ${event.metadata.oldCondition} to ${event.metadata.newCondition}`;
          }
          break;
      }
    }

    return details;
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Usage History
          </CardTitle>
          <div className="flex items-center gap-2">
            {/* Event type filter */}
            {availableEventTypes.length > 1 && (
              <div className="flex items-center gap-1">
                <Button
                  variant={filterType === null ? "default" : "outline"}
                  size="sm"
                  onClick={() => handleFilterChange(null)}
                >
                  All
                </Button>
                {availableEventTypes.map((type) => {
                  const config = EVENT_TYPE_CONFIG[type];
                  const Icon = config.icon;
                  return (
                    <Button
                      key={type}
                      variant={filterType === type ? "default" : "outline"}
                      size="sm"
                      onClick={() => handleFilterChange(type)}
                    >
                      <Icon className="h-3 w-3 mr-1" />
                      {config.label}
                    </Button>
                  );
                })}
              </div>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {/* Usage statistics summary */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 p-4 bg-muted rounded-lg">
            <div className="text-center">
              <div className="text-2xl font-bold text-primary">{usageStats.totalViews}</div>
              <div className="text-sm text-muted-foreground">Total Views</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-primary">{usageStats.totalEdits}</div>
              <div className="text-sm text-muted-foreground">Total Edits</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-primary">{usageStats.locationChanges}</div>
              <div className="text-sm text-muted-foreground">Location Changes</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-primary">
                {usageStats.editFrequency.toFixed(2)}
              </div>
              <div className="text-sm text-muted-foreground">Edits/Day</div>
            </div>
          </div>

          {/* Events timeline */}
          {filteredEvents.length === 0 ? (
            <div className="text-center py-8">
              <Clock className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">No Usage History</h3>
              <p className="text-muted-foreground">
                {filterType 
                  ? `No ${EVENT_TYPE_CONFIG[filterType].label.toLowerCase()} events found.`
                  : "This disc hasn't been used yet."
                }
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              <h3 className="font-medium flex items-center gap-2">
                <TrendingUp className="h-4 w-4" />
                Recent Activity
                {filterType && (
                  <Badge variant="secondary" className="ml-2">
                    {EVENT_TYPE_CONFIG[filterType].label} only
                  </Badge>
                )}
              </h3>
              
              <div className="space-y-3">
                {filteredEvents.map((event, index) => {
                  const config = EVENT_TYPE_CONFIG[event.type];
                  const Icon = config.icon;
                  const isRecent = index < 3;

                  return (
                    <div
                      key={event.id}
                      className={`flex items-start gap-3 p-3 rounded-lg border ${
                        isRecent ? "bg-accent/50" : "bg-background"
                      }`}
                    >
                      {/* Event icon */}
                      <div className={`p-2 rounded-full ${config.color}`}>
                        <Icon className="h-4 w-4" />
                      </div>

                      {/* Event details */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <h4 className="font-medium text-sm">{config.label}</h4>
                          <time className="text-xs text-muted-foreground">
                            {formatRelativeTime(event.timestamp)}
                          </time>
                        </div>
                        <p className="text-sm text-muted-foreground mt-1">
                          {formatEventDetails(event)}
                        </p>
                        {event.metadata?.source && (
                          <Badge variant="outline" className="mt-2 text-xs">
                            {event.metadata.source}
                          </Badge>
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>

              {/* Show more button */}
              {!showAllEvents && usageHistory.length > maxEvents && (
                <div className="text-center pt-4">
                  <Button
                    variant="outline"
                    onClick={() => setShowAllEvents(true)}
                  >
                    Show All {usageHistory.length} Events
                  </Button>
                </div>
              )}

              {showAllEvents && usageHistory.length > maxEvents && (
                <div className="text-center pt-4">
                  <Button
                    variant="outline"
                    onClick={() => setShowAllEvents(false)}
                  >
                    Show Recent Only
                  </Button>
                </div>
              )}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
