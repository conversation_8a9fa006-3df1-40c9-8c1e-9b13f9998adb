/**
 * Disc Detail Page Component for Disc Golf Inventory Management System
 *
 * This component provides a comprehensive view of a single disc including:
 * - Complete disc specifications and metadata
 * - Edit capabilities with inline editing
 * - Usage history tracking and display
 * - Related disc suggestions
 * - Comparison tools
 *
 * Requirements Satisfied:
 * - FR-DETAIL-001: Detailed view with all disc information, edit capabilities, and usage history
 * - FR-DETAIL-002: Related disc suggestions and comparison tools
 * - FR-DETAIL-003: Inline editing with real-time validation
 */

"use client";

import * as React from "react";
import { useRouter } from "next/navigation";
import { notFound } from "next/navigation";
import { Layout, PageContainer, Section } from "@/components/layout";
import { Button } from "@/components/ui/button";
import { Breadcrumb, useDiscDetailBreadcrumb } from "@/components/ui/breadcrumb";
import { useInventory } from "@/hooks/useInventory";
import { useUsageHistory } from "@/hooks/useUsageHistory";
import { DiscDetailHeader } from "./DiscDetailHeader";
import { DiscDetailInfo } from "./DiscDetailInfo";
import { DiscDetailActions } from "./DiscDetailActions";
import { DiscUsageHistory } from "./DiscUsageHistory";
import { RelatedDiscs } from "./RelatedDiscs";
import { formatDiscDisplayName } from "@/lib/discUtils";
import { ArrowLeft, Edit, Share, MoreHorizontal } from "lucide-react";
import { UsageEventType } from "@/lib/usageHistory";

// ============================================================================
// TYPES
// ============================================================================

interface DiscDetailPageProps {
  discId: string;
}

// ============================================================================
// COMPONENT
// ============================================================================

/**
 * Disc Detail Page Component
 *
 * Main component that orchestrates the display of disc details, usage history,
 * and related functionality. Handles data loading, error states, and user interactions.
 *
 * @param props - Component props
 * @returns JSX element
 */
export function DiscDetailPage({ discId }: DiscDetailPageProps) {
  const router = useRouter();
  const { getDisc, updateDisc, loading: inventoryLoading, error: inventoryError } = useInventory();
  const { trackEvent, getDiscHistory, getDiscStats, startViewTracking, stopViewTracking } = useUsageHistory();

  // State
  const [isEditing, setIsEditing] = React.useState(false);
  const [showComparison, setShowComparison] = React.useState(false);

  // Get disc data
  const disc = getDisc(discId);

  // Track page view
  React.useEffect(() => {
    if (disc) {
      // Start tracking view time
      (startViewTracking as any)?.(discId);

      // Record view event
      trackEvent(discId, UsageEventType.VIEW, {
        referrer: document.referrer || undefined,
      });
    }

    // Cleanup on unmount
    return () => {
      (stopViewTracking as any)?.();
    };
  }, [disc, discId, trackEvent, startViewTracking, stopViewTracking]);

  // Handle disc not found
  if (!inventoryLoading && !disc) {
    notFound();
  }

  // Handle loading state
  if (inventoryLoading || !disc) {
    return null; // Loading component will be shown by loading.tsx
  }

  // Handle error state
  if (inventoryError) {
    throw new Error(inventoryError.message);
  }

  // Event handlers
  const handleEdit = () => {
    setIsEditing(true);
    trackEvent(discId, UsageEventType.EDIT, {
      source: "manual",
    });
  };

  const handleSaveEdit = async (updatedDisc: any) => {
    const result = await updateDisc(updatedDisc);
    if (result.success) {
      setIsEditing(false);
      trackEvent(discId, UsageEventType.EDIT, {
        source: "manual",
        fieldsChanged: Object.keys(updatedDisc).filter((key) => key !== "id"),
      });
    }
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
  };

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: `${disc.manufacturer} ${disc.mold}`,
          text: `Check out this ${disc.manufacturer} ${disc.mold} in ${disc.plasticType} plastic`,
          url: window.location.href,
        });
      } catch (error) {
        // User cancelled or error occurred
        console.log("Share cancelled or failed:", error);
      }
    } else {
      // Fallback: copy to clipboard
      try {
        await navigator.clipboard.writeText(window.location.href);
        // Could show a toast notification here
      } catch (error) {
        console.error("Failed to copy to clipboard:", error);
      }
    }
  };

  const handleBackToInventory = () => {
    router.push("/inventory");
  };

  // Get usage data
  const usageHistory = getDiscHistory(discId);
  const usageStats = getDiscStats(discId);

  // Generate breadcrumb items
  const breadcrumbItems = useDiscDetailBreadcrumb(formatDiscDisplayName(disc));

  return (
    <Layout>
      <PageContainer
        title={formatDiscDisplayName(disc)}
        description={`${disc.manufacturer} ${disc.mold} in ${disc.plasticType} plastic`}
        actions={
          <DiscDetailActions
            disc={disc}
            isEditing={isEditing}
            onEdit={handleEdit}
            onSave={handleSaveEdit}
            onCancel={handleCancelEdit}
            onShare={handleShare}
            onBack={handleBackToInventory}
          />
        }
      >
        {/* Breadcrumb navigation */}
        <div className="mb-6">
          <Breadcrumb items={breadcrumbItems} />
        </div>
        <div className="space-y-6">
          {/* Main disc information */}
          <Section>
            <DiscDetailInfo
              disc={disc}
              isEditing={isEditing}
              onSave={handleSaveEdit}
              onCancel={handleCancelEdit}
              usageStats={usageStats}
            />
          </Section>

          {/* Usage history */}
          <Section>
            <DiscUsageHistory discId={discId} usageHistory={usageHistory} usageStats={usageStats} />
          </Section>

          {/* Related discs */}
          <Section>
            <RelatedDiscs
              currentDisc={disc}
              onDiscSelect={(selectedDisc) => {
                router.push(`/inventory/${selectedDisc.id}`);
              }}
              showComparison={showComparison}
              onToggleComparison={() => setShowComparison(!showComparison)}
            />
          </Section>
        </div>
      </PageContainer>
    </Layout>
  );
}
