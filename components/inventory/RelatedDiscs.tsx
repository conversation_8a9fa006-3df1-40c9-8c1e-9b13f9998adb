/**
 * Related Discs Component for Disc Golf Inventory Management System
 *
 * This component displays disc recommendations based on the current disc,
 * including similar discs, complementary options, and bag gap fillers.
 *
 * Features:
 * - Intelligent disc recommendations
 * - Similarity scoring and explanations
 * - Comparison mode toggle
 * - Interactive disc cards
 * - Loading and empty states
 */

"use client";

import * as React from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { DiscCard } from "./DiscCard";
import { useInventory } from "@/hooks/useInventory";
import { generateDiscRecommendations, DEFAULT_RECOMMENDATION_CONFIG, type DiscRecommendation } from "@/lib/discRecommendations";
import { formatDiscDisplayName } from "@/lib/discUtils";
import { Lightbulb, GitCompare, ExternalLink, Shuffle } from "lucide-react";
import type { Disc } from "@/lib/types";

// ============================================================================
// TYPES
// ============================================================================

interface RelatedDiscsProps {
  currentDisc: Disc;
  onDiscSelect: (disc: Disc) => void;
  showComparison: boolean;
  onToggleComparison: () => void;
  maxRecommendations?: number;
}

// ============================================================================
// COMPONENT
// ============================================================================

/**
 * Related Discs Component
 *
 * Displays intelligent disc recommendations based on the current disc
 * and the user's collection. Provides explanations for why each disc
 * is recommended and allows comparison mode.
 *
 * @param props - Component props
 * @returns JSX element
 */
export function RelatedDiscs({
  currentDisc,
  onDiscSelect,
  showComparison,
  onToggleComparison,
  maxRecommendations = 6,
}: RelatedDiscsProps) {
  const { discs } = useInventory();
  const [recommendations, setRecommendations] = React.useState<DiscRecommendation[]>([]);
  const [isLoading, setIsLoading] = React.useState(true);

  // Generate recommendations when disc or collection changes
  React.useEffect(() => {
    const generateRecommendations = async () => {
      setIsLoading(true);
      
      try {
        // In a real app, this would come from an API or database
        // For now, we'll use the current collection as the "all discs" source
        // and filter out the current disc
        const allDiscs = discs.filter(disc => disc.id !== currentDisc.id);
        
        const config = {
          ...DEFAULT_RECOMMENDATION_CONFIG,
          maxRecommendations,
          includeOwnedDiscs: true, // Include owned discs for now since we don't have external data
        };

        const recs = generateDiscRecommendations(currentDisc, discs, allDiscs, config);
        setRecommendations(recs);
      } catch (error) {
        console.error("Failed to generate recommendations:", error);
        setRecommendations([]);
      } finally {
        setIsLoading(false);
      }
    };

    generateRecommendations();
  }, [currentDisc, discs, maxRecommendations]);

  const handleDiscClick = (disc: Disc) => {
    onDiscSelect(disc);
  };

  const handleRefreshRecommendations = () => {
    // Trigger a refresh by updating the effect dependency
    setIsLoading(true);
    setTimeout(() => {
      setIsLoading(false);
    }, 500);
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Lightbulb className="h-5 w-5" />
            Related Discs
          </CardTitle>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefreshRecommendations}
              disabled={isLoading}
            >
              <Shuffle className="h-4 w-4 mr-2" />
              Refresh
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={onToggleComparison}
            >
              <GitCompare className="h-4 w-4 mr-2" />
              {showComparison ? "Hide" : "Show"} Comparison
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="space-y-4">
            <div className="text-center text-muted-foreground">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
              <p>Finding related discs...</p>
            </div>
          </div>
        ) : recommendations.length === 0 ? (
          <div className="text-center py-8">
            <Lightbulb className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">No Related Discs Found</h3>
            <p className="text-muted-foreground mb-4">
              We couldn't find any related discs in your collection. Try adding more discs to get better recommendations.
            </p>
            <Button variant="outline" onClick={handleRefreshRecommendations}>
              <Shuffle className="h-4 w-4 mr-2" />
              Try Again
            </Button>
          </div>
        ) : (
          <div className="space-y-6">
            {/* Recommendations grid */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
              {recommendations.map((recommendation) => (
                <div key={recommendation.disc.id} className="space-y-3">
                  {/* Disc card */}
                  <div className="relative">
                    <DiscCard
                      disc={recommendation.disc}
                      onSelect={handleDiscClick}
                      showActions={false}
                      className="cursor-pointer hover:shadow-md transition-shadow"
                    />
                    
                    {/* Similarity score badge */}
                    <div className="absolute top-2 right-2">
                      <Badge variant="secondary" className="text-xs">
                        {Math.round(recommendation.similarity * 100)}% match
                      </Badge>
                    </div>
                  </div>

                  {/* Recommendation explanation */}
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <h4 className="font-medium text-sm">
                        {formatDiscDisplayName(recommendation.disc)}
                      </h4>
                      <div className="flex items-center gap-1">
                        <div className="flex">
                          {[...Array(5)].map((_, i) => (
                            <div
                              key={i}
                              className={`h-2 w-2 rounded-full ${
                                i < Math.round(recommendation.score * 5)
                                  ? "bg-primary"
                                  : "bg-muted"
                              }`}
                            />
                          ))}
                        </div>
                      </div>
                    </div>
                    
                    <p className="text-xs text-muted-foreground">
                      {recommendation.explanation}
                    </p>

                    {/* Action button */}
                    <Button
                      variant="ghost"
                      size="sm"
                      className="w-full justify-start h-8"
                      onClick={() => handleDiscClick(recommendation.disc)}
                    >
                      <ExternalLink className="h-3 w-3 mr-2" />
                      View Details
                    </Button>
                  </div>
                </div>
              ))}
            </div>

            {/* Comparison mode */}
            {showComparison && (
              <div className="border-t pt-6">
                <h3 className="font-medium mb-4 flex items-center gap-2">
                  <GitCompare className="h-4 w-4" />
                  Flight Comparison
                </h3>
                <div className="space-y-4">
                  {recommendations.slice(0, 3).map((recommendation) => (
                    <div
                      key={recommendation.disc.id}
                      className="flex items-center justify-between p-3 bg-muted rounded-lg"
                    >
                      <div className="flex items-center gap-3">
                        <div className="text-sm font-medium">
                          {formatDiscDisplayName(recommendation.disc)}
                        </div>
                        <div className="flex gap-1 text-xs text-muted-foreground">
                          <span>{recommendation.disc.flightNumbers.speed}</span>
                          <span>|</span>
                          <span>{recommendation.disc.flightNumbers.glide}</span>
                          <span>|</span>
                          <span>{recommendation.disc.flightNumbers.turn}</span>
                          <span>|</span>
                          <span>{recommendation.disc.flightNumbers.fade}</span>
                        </div>
                      </div>
                      <div className="text-xs text-muted-foreground">
                        vs Current: {currentDisc.flightNumbers.speed}|{currentDisc.flightNumbers.glide}|{currentDisc.flightNumbers.turn}|{currentDisc.flightNumbers.fade}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Show more button if there are more recommendations */}
            {recommendations.length >= maxRecommendations && (
              <div className="text-center pt-4 border-t">
                <Button variant="outline" onClick={handleRefreshRecommendations}>
                  <Shuffle className="h-4 w-4 mr-2" />
                  Show Different Recommendations
                </Button>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
