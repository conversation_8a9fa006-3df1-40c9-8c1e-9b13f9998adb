/**
 * Disc Detail Info Component for Disc Golf Inventory Management System
 *
 * This component displays comprehensive disc information including:
 * - Disc image and specifications
 * - Flight numbers with visualization
 * - Editable fields with inline editing
 * - Usage statistics integration
 */

"use client";

import * as React from "react";
import Image from "next/image";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { FlightNumbersDisplay } from "@/components/ui/FlightNumbersDisplay";
import { EditDiscForm } from "@/components/forms/EditDiscForm";
import { formatDiscDisplayName, formatCurrency, formatDate } from "@/lib/discUtils";
import { Calendar, DollarSign, MapPin, Palette, Scale, Eye, Edit as EditIcon } from "lucide-react";
import type { Disc } from "@/lib/types";
import type { DiscUsageStats } from "@/lib/usageHistory";

// ============================================================================
// TYPES
// ============================================================================

interface DiscDetailInfoProps {
  disc: Disc;
  isEditing: boolean;
  onSave: (disc: Disc) => void;
  onCancel: () => void;
  usageStats: DiscUsageStats;
}

// ============================================================================
// COMPONENT
// ============================================================================

/**
 * Disc Detail Info Component
 *
 * Displays detailed disc information with support for inline editing.
 * Shows disc image, specifications, metadata, and usage statistics.
 *
 * @param props - Component props
 * @returns JSX element
 */
export function DiscDetailInfo({ disc, isEditing, onSave, onCancel, usageStats }: DiscDetailInfoProps) {
  const [imageError, setImageError] = React.useState(false);
  const displayName = formatDiscDisplayName(disc);

  if (isEditing) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <EditIcon className="h-5 w-5" />
            Edit Disc Details
          </CardTitle>
        </CardHeader>
        <CardContent>
          <EditDiscForm disc={disc} onSave={onSave} onCancel={onCancel} showCard={false} />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Eye className="h-5 w-5" />
          Disc Details
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Left column: Image and basic info */}
          <div className="space-y-4">
            {/* Disc image */}
            <div className="relative aspect-square w-full max-w-md mx-auto overflow-hidden rounded-lg bg-muted">
              {disc.imageUrl && !imageError ? (
                <Image
                  src={disc.imageUrl}
                  alt={`${displayName} disc`}
                  fill
                  className="object-cover"
                  sizes="(max-width: 768px) 100vw, 50vw"
                  onError={() => setImageError(true)}
                  priority
                />
              ) : (
                <div className="flex items-center justify-center h-full text-muted-foreground">
                  <div className="text-center">
                    <div className="text-6xl mb-2">🥏</div>
                    <p className="text-sm">No image available</p>
                  </div>
                </div>
              )}
            </div>

            {/* Flight numbers */}
            <div className="space-y-2">
              <h3 className="font-medium text-sm text-muted-foreground">Flight Numbers</h3>
              <FlightNumbersDisplay
                flightNumbers={disc.flightNumbers}
                size="lg"
                showLabels={true}
                showTooltips={true}
              />
            </div>

            {/* Color indicator */}
            <div className="space-y-2">
              <h3 className="font-medium text-sm text-muted-foreground">Color</h3>
              <div className="flex items-center gap-3">
                <div
                  className="h-8 w-8 rounded-full border-2 border-border shadow-sm"
                  style={{ backgroundColor: disc.color }}
                  aria-label={`Disc color: ${disc.color}`}
                />
                <span className="text-sm font-medium capitalize">{disc.color}</span>
              </div>
            </div>
          </div>

          {/* Right column: Detailed specifications */}
          <div className="space-y-6">
            {/* Basic specifications */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <h3 className="font-medium text-sm text-muted-foreground mb-1">Manufacturer</h3>
                <p className="font-medium">{disc.manufacturer}</p>
              </div>
              <div>
                <h3 className="font-medium text-sm text-muted-foreground mb-1">Mold</h3>
                <p className="font-medium">{disc.mold}</p>
              </div>
              <div>
                <h3 className="font-medium text-sm text-muted-foreground mb-1">Plastic Type</h3>
                <p className="font-medium">{disc.plasticType}</p>
              </div>
              <div>
                <h3 className="font-medium text-sm text-muted-foreground mb-1">Weight</h3>
                <div className="flex items-center gap-1">
                  <Scale className="h-4 w-4 text-muted-foreground" />
                  <span className="font-medium">{disc.weight}g</span>
                </div>
              </div>
            </div>

            {/* Location and condition */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <h3 className="font-medium text-sm text-muted-foreground mb-1">Location</h3>
                <div className="flex items-center gap-1">
                  <MapPin className="h-4 w-4 text-muted-foreground" />
                  <span className="font-medium capitalize">{disc.currentLocation}</span>
                </div>
              </div>
              <div>
                <h3 className="font-medium text-sm text-muted-foreground mb-1">Condition</h3>
                <Badge variant="secondary" className="capitalize">
                  {disc.condition}
                </Badge>
              </div>
            </div>

            {/* Purchase information */}
            {(disc.purchaseDate || disc.purchasePrice) && (
              <div className="space-y-3">
                <h3 className="font-medium text-sm text-muted-foreground">Purchase Information</h3>
                <div className="grid grid-cols-2 gap-4">
                  {disc.purchaseDate && (
                    <div>
                      <div className="flex items-center gap-1 text-sm text-muted-foreground mb-1">
                        <Calendar className="h-4 w-4" />
                        Purchase Date
                      </div>
                      <p className="font-medium">{formatDate(disc.purchaseDate)}</p>
                    </div>
                  )}
                  {disc.purchasePrice && (
                    <div>
                      <div className="flex items-center gap-1 text-sm text-muted-foreground mb-1">
                        <DollarSign className="h-4 w-4" />
                        Purchase Price
                      </div>
                      <p className="font-medium">{formatCurrency(disc.purchasePrice)}</p>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Usage statistics */}
            <div className="space-y-3">
              <h3 className="font-medium text-sm text-muted-foreground">Usage Statistics</h3>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-muted-foreground">Total Views:</span>
                  <span className="ml-2 font-medium">{usageStats.totalViews}</span>
                </div>
                <div>
                  <span className="text-muted-foreground">Total Edits:</span>
                  <span className="ml-2 font-medium">{usageStats.totalEdits}</span>
                </div>
                {usageStats.lastViewed && (
                  <div className="col-span-2">
                    <span className="text-muted-foreground">Last Viewed:</span>
                    <span className="ml-2 font-medium">{formatDate(usageStats.lastViewed)}</span>
                  </div>
                )}
              </div>
            </div>

            {/* Notes */}
            {disc.notes && (
              <div className="space-y-2">
                <h3 className="font-medium text-sm text-muted-foreground">Notes</h3>
                <p className="text-sm bg-muted p-3 rounded-md">{disc.notes}</p>
              </div>
            )}

            {/* Metadata */}
            <div className="space-y-2 pt-4 border-t">
              <h3 className="font-medium text-sm text-muted-foreground">Metadata</h3>
              <div className="text-xs text-muted-foreground space-y-1">
                <p>Created: {formatDate(disc.createdAt)}</p>
                <p>Updated: {formatDate(disc.updatedAt)}</p>
                <p>ID: {disc.id}</p>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
