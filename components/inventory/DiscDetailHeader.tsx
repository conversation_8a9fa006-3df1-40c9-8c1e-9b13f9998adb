/**
 * Disc Detail Header Component for Disc Golf Inventory Management System
 *
 * This component displays the header information for a disc detail page,
 * including the disc name, condition badge, and edit state indicator.
 */

"use client";

import * as React from "react";
import { Badge } from "@/components/ui/badge";
import { DiscBadge } from "@/components/ui/DiscBadge";
import { formatDiscDisplayName } from "@/lib/discUtils";
import { Edit, Eye } from "lucide-react";
import type { Disc } from "@/lib/types";

// ============================================================================
// TYPES
// ============================================================================

interface DiscDetailHeaderProps {
  disc: Disc;
  isEditing: boolean;
}

// ============================================================================
// COMPONENT
// ============================================================================

/**
 * Disc Detail Header Component
 *
 * Displays the main header for a disc detail page with:
 * - Formatted disc name (manufacturer + mold)
 * - Condition badge
 * - Edit mode indicator
 * - Visual hierarchy and styling
 *
 * @param props - Component props
 * @returns JSX element
 */
export function DiscDetailHeader({ disc, isEditing }: DiscDetailHeaderProps) {
  const displayName = formatDiscDisplayName(disc);

  return (
    <div className="flex items-center gap-3">
      {/* Main disc name */}
      <h1 className="text-2xl font-bold text-foreground">{displayName}</h1>

      {/* Condition badge */}
      <DiscBadge condition={disc.condition} size="md" />

      {/* Edit mode indicator */}
      {isEditing && (
        <Badge variant="secondary" className="flex items-center gap-1">
          <Edit className="h-3 w-3" />
          Editing
        </Badge>
      )}

      {/* View mode indicator (when not editing) */}
      {!isEditing && (
        <Badge variant="outline" className="flex items-center gap-1">
          <Eye className="h-3 w-3" />
          Viewing
        </Badge>
      )}
    </div>
  );
}
