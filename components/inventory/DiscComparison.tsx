/**
 * Disc Comparison Component for Disc Golf Inventory Management System
 *
 * This component provides side-by-side comparison of discs including:
 * - Flight numbers visualization and differences
 * - Specifications comparison table
 * - Visual difference highlighting
 * - Similarity scoring
 * - Recommendation insights
 *
 * Features:
 * - Interactive comparison interface
 * - Difference highlighting with color coding
 * - Flight path visualization
 * - Detailed specifications breakdown
 * - Export comparison results
 */

"use client";

import * as React from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { FlightNumbersDisplay } from "@/components/ui/FlightNumbersDisplay";
import { DiscBadge } from "@/components/ui/DiscBadge";
import { formatDiscDisplayName, formatCurrency, formatDate } from "@/lib/discUtils";
// Note: calculateFlightSimilarity is defined in discRecommendations but we'll implement it locally to avoid circular imports
const calculateFlightSimilarity = (flight1: any, flight2: any): number => {
  const speedDiff = Math.abs(flight1.speed - flight2.speed) / 14;
  const glideDiff = Math.abs(flight1.glide - flight2.glide) / 7;
  const turnDiff = Math.abs(flight1.turn - flight2.turn) / 6;
  const fadeDiff = Math.abs(flight1.fade - flight2.fade) / 5;

  const weightedDiff = speedDiff * 0.3 + glideDiff * 0.2 + turnDiff * 0.3 + fadeDiff * 0.2;
  return Math.max(0, 1 - weightedDiff);
};
import { GitCompare, TrendingUp, TrendingDown, Minus, ArrowRight, Download, X } from "lucide-react";
import type { Disc } from "@/lib/types";

// ============================================================================
// TYPES
// ============================================================================

interface DiscComparisonProps {
  disc1: Disc;
  disc2: Disc;
  onClose?: () => void;
  showExportButton?: boolean;
}

interface ComparisonField {
  label: string;
  key: keyof Disc | string;
  getValue: (disc: Disc) => string | number | React.ReactNode;
  formatDifference?: (value1: any, value2: any) => React.ReactNode;
}

// ============================================================================
// CONSTANTS
// ============================================================================

/**
 * Fields to compare between discs
 */
const COMPARISON_FIELDS: ComparisonField[] = [
  {
    label: "Manufacturer",
    key: "manufacturer",
    getValue: (disc) => disc.manufacturer,
  },
  {
    label: "Mold",
    key: "mold",
    getValue: (disc) => disc.mold,
  },
  {
    label: "Plastic Type",
    key: "plasticType",
    getValue: (disc) => disc.plasticType,
  },
  {
    label: "Weight",
    key: "weight",
    getValue: (disc) => `${disc.weight}g`,
    formatDifference: (weight1, weight2) => {
      const diff = weight2 - weight1;
      if (diff === 0) return <Minus className="h-4 w-4 text-muted-foreground" />;
      return (
        <div className={`flex items-center gap-1 ${diff > 0 ? "text-green-600" : "text-red-600"}`}>
          {diff > 0 ? <TrendingUp className="h-4 w-4" /> : <TrendingDown className="h-4 w-4" />}
          <span>{Math.abs(diff)}g</span>
        </div>
      );
    },
  },
  {
    label: "Condition",
    key: "condition",
    getValue: (disc) => <DiscBadge condition={disc.condition} size="sm" />,
  },
  {
    label: "Location",
    key: "currentLocation",
    getValue: (disc) => (
      <Badge variant="outline" className="capitalize">
        {disc.currentLocation}
      </Badge>
    ),
  },
  {
    label: "Color",
    key: "color",
    getValue: (disc) => (
      <div className="flex items-center gap-2">
        <div className="h-4 w-4 rounded-full border border-border" style={{ backgroundColor: disc.color }} />
        <span className="capitalize">{disc.color}</span>
      </div>
    ),
  },
  {
    label: "Purchase Price",
    key: "purchasePrice",
    getValue: (disc) => (disc.purchasePrice ? formatCurrency(disc.purchasePrice) : "—"),
    formatDifference: (price1, price2) => {
      if (!price1 || !price2) return <Minus className="h-4 w-4 text-muted-foreground" />;
      const diff = price2 - price1;
      if (diff === 0) return <Minus className="h-4 w-4 text-muted-foreground" />;
      return (
        <div className={`flex items-center gap-1 ${diff > 0 ? "text-red-600" : "text-green-600"}`}>
          {diff > 0 ? <TrendingUp className="h-4 w-4" /> : <TrendingDown className="h-4 w-4" />}
          <span>{formatCurrency(Math.abs(diff))}</span>
        </div>
      );
    },
  },
  {
    label: "Purchase Date",
    key: "purchaseDate",
    getValue: (disc) => (disc.purchaseDate ? formatDate(disc.purchaseDate) : "—"),
  },
];

// ============================================================================
// COMPONENT
// ============================================================================

/**
 * Disc Comparison Component
 *
 * Provides detailed side-by-side comparison of two discs with
 * visual difference highlighting and similarity analysis.
 *
 * @param props - Component props
 * @returns JSX element
 */
export function DiscComparison({ disc1, disc2, onClose, showExportButton = true }: DiscComparisonProps) {
  // Calculate flight similarity
  const flightSimilarity = React.useMemo(() => {
    return calculateFlightSimilarity(disc1.flightNumbers, disc2.flightNumbers);
  }, [disc1.flightNumbers, disc2.flightNumbers]);

  // Calculate overall similarity
  const overallSimilarity = React.useMemo(() => {
    let matches = 0;
    let total = 0;

    // Check manufacturer match
    total++;
    if (disc1.manufacturer === disc2.manufacturer) matches++;

    // Check mold match
    total++;
    if (disc1.mold === disc2.mold) matches++;

    // Check plastic type match
    total++;
    if (disc1.plasticType === disc2.plasticType) matches++;

    // Weight similarity (within 5g)
    total++;
    if (Math.abs(disc1.weight - disc2.weight) <= 5) matches++;

    // Flight similarity (weighted)
    const flightScore = flightSimilarity * 0.4; // Flight numbers are 40% of similarity
    const basicScore = (matches / total) * 0.6; // Other factors are 60%

    return flightScore + basicScore;
  }, [disc1, disc2, flightSimilarity]);

  const handleExport = () => {
    const comparisonData = {
      disc1: {
        name: formatDiscDisplayName(disc1),
        ...disc1,
      },
      disc2: {
        name: formatDiscDisplayName(disc2),
        ...disc2,
      },
      similarity: {
        overall: overallSimilarity,
        flight: flightSimilarity,
      },
      timestamp: new Date().toISOString(),
    };

    const blob = new Blob([JSON.stringify(comparisonData, null, 2)], {
      type: "application/json",
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `disc-comparison-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <GitCompare className="h-5 w-5" />
            Disc Comparison
          </CardTitle>
          <div className="flex items-center gap-2">
            {showExportButton && (
              <Button variant="outline" size="sm" onClick={handleExport}>
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
            )}
            {onClose && (
              <Button variant="ghost" size="sm" onClick={onClose}>
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Similarity overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-muted rounded-lg">
          <div className="text-center">
            <div className="text-2xl font-bold text-primary">{Math.round(overallSimilarity * 100)}%</div>
            <div className="text-sm text-muted-foreground">Overall Similarity</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-primary">{Math.round(flightSimilarity * 100)}%</div>
            <div className="text-sm text-muted-foreground">Flight Similarity</div>
          </div>
        </div>

        {/* Disc headers */}
        <div className="grid grid-cols-3 gap-4 items-center">
          <div className="text-center">
            <h3 className="font-semibold text-lg">{formatDiscDisplayName(disc1)}</h3>
            <p className="text-sm text-muted-foreground">Disc A</p>
          </div>
          <div className="text-center">
            <ArrowRight className="h-6 w-6 mx-auto text-muted-foreground" />
          </div>
          <div className="text-center">
            <h3 className="font-semibold text-lg">{formatDiscDisplayName(disc2)}</h3>
            <p className="text-sm text-muted-foreground">Disc B</p>
          </div>
        </div>

        {/* Flight numbers comparison */}
        <div className="space-y-4">
          <h4 className="font-medium">Flight Numbers</h4>
          <div className="grid grid-cols-3 gap-4 items-center">
            <div className="text-center">
              <FlightNumbersDisplay
                flightNumbers={disc1.flightNumbers}
                size="md"
                showLabels={true}
                showTooltips={false}
              />
            </div>
            <div className="text-center">
              <div className="space-y-2">
                <div className="text-xs text-muted-foreground">Differences</div>
                <div className="grid grid-cols-4 gap-1 text-xs">
                  {["speed", "glide", "turn", "fade"].map((key) => {
                    const diff =
                      disc2.flightNumbers[key as keyof typeof disc2.flightNumbers] -
                      disc1.flightNumbers[key as keyof typeof disc1.flightNumbers];
                    return (
                      <div
                        key={key}
                        className={`p-1 rounded text-center ${
                          diff === 0
                            ? "bg-muted text-muted-foreground"
                            : diff > 0
                            ? "bg-green-100 text-green-800"
                            : "bg-red-100 text-red-800"
                        }`}
                      >
                        {diff > 0 ? `+${diff}` : diff}
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
            <div className="text-center">
              <FlightNumbersDisplay
                flightNumbers={disc2.flightNumbers}
                size="md"
                showLabels={true}
                showTooltips={false}
              />
            </div>
          </div>
        </div>

        {/* Specifications comparison */}
        <div className="space-y-4">
          <h4 className="font-medium">Specifications</h4>
          <div className="space-y-3">
            {COMPARISON_FIELDS.map((field) => {
              const value1 = field.getValue(disc1);
              const value2 = field.getValue(disc2);
              const rawValue1 = typeof value1 === "object" ? disc1[field.key as keyof Disc] : value1;
              const rawValue2 = typeof value2 === "object" ? disc2[field.key as keyof Disc] : value2;
              const isEqual = rawValue1 === rawValue2;

              return (
                <div
                  key={field.key}
                  className={`grid grid-cols-3 gap-4 items-center p-3 rounded-lg border ${
                    isEqual ? "bg-green-50 border-green-200" : "bg-background"
                  }`}
                >
                  <div className="text-center">{value1}</div>
                  <div className="text-center">
                    <div className="text-xs text-muted-foreground mb-1">{field.label}</div>
                    {field.formatDifference && !isEqual ? (
                      field.formatDifference(rawValue1, rawValue2)
                    ) : isEqual ? (
                      <Badge variant="secondary" className="text-xs">
                        Same
                      </Badge>
                    ) : (
                      <Badge variant="outline" className="text-xs">
                        Different
                      </Badge>
                    )}
                  </div>
                  <div className="text-center">{value2}</div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Notes comparison */}
        {(disc1.notes || disc2.notes) && (
          <div className="space-y-4">
            <h4 className="font-medium">Notes</h4>
            <div className="grid grid-cols-2 gap-4">
              <div className="p-3 bg-muted rounded-lg">
                <div className="text-sm font-medium mb-2">Disc A Notes</div>
                <p className="text-sm text-muted-foreground">{disc1.notes || "No notes"}</p>
              </div>
              <div className="p-3 bg-muted rounded-lg">
                <div className="text-sm font-medium mb-2">Disc B Notes</div>
                <p className="text-sm text-muted-foreground">{disc2.notes || "No notes"}</p>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
