# Disc Detail Pages Usage Guide

## Overview

This guide provides comprehensive examples and testing recommendations for the Disc Detail Pages implementation in the Disc Golf Inventory Management System.

## Usage Examples

### 1. Basic Navigation to Detail Page

```typescript
// From inventory listing - automatic navigation via DiscCard
<DiscCard 
  disc={disc} 
  enableNavigation={true} // Default: true
  // No onSelect handler = automatic navigation to /inventory/{disc.id}
/>

// Programmatic navigation
import { useRouter } from 'next/navigation';

const router = useRouter();
const navigateToDisc = (discId: string) => {
  router.push(`/inventory/${discId}`);
};
```

### 2. Usage History Tracking

```typescript
// Automatic tracking in DiscDetailPage
import { useUsageHistory } from '@/hooks/useUsageHistory';

const { trackEvent, getDiscHistory, getDiscStats } = useUsageHistory();

// Manual event tracking
await trackEvent(discId, UsageEventType.VIEW, {
  referrer: document.referrer,
  viewDuration: 5000, // milliseconds
});

// Get usage data
const history = getDiscHistory(discId);
const stats = getDiscStats(discId);
```

### 3. Inline Editing

```typescript
// EditDiscForm usage
import { EditDiscForm } from '@/components/forms/EditDiscForm';

<EditDiscForm
  disc={disc}
  onSave={async (updatedDisc) => {
    const result = await updateDisc(updatedDisc);
    if (result.success) {
      setIsEditing(false);
    }
  }}
  onCancel={() => setIsEditing(false)}
  showCard={false} // For inline editing
/>
```

### 4. Related Disc Recommendations

```typescript
// RelatedDiscs component usage
import { RelatedDiscs } from '@/components/inventory/RelatedDiscs';

<RelatedDiscs
  currentDisc={disc}
  onDiscSelect={(selectedDisc) => {
    router.push(`/inventory/${selectedDisc.id}`);
  }}
  showComparison={showComparison}
  onToggleComparison={() => setShowComparison(!showComparison)}
  maxRecommendations={6}
/>
```

### 5. Disc Comparison

```typescript
// DiscComparison component usage
import { DiscComparison } from '@/components/inventory/DiscComparison';

<DiscComparison
  disc1={currentDisc}
  disc2={comparisonDisc}
  onClose={() => setShowComparison(false)}
  showExportButton={true}
/>
```

### 6. Custom Usage History Events

```typescript
// Track custom events
const { trackLocationChange, trackConditionChange } = useUsageHistory();

// Location change tracking
await trackLocationChange(discId, 'home', 'bag');

// Condition change tracking  
await trackConditionChange(discId, 'new', 'good');

// Custom edit tracking with field details
await trackEdit(discId, {
  fieldsChanged: ['weight', 'notes'],
  oldValues: { weight: 175, notes: 'Original notes' },
  newValues: { weight: 173, notes: 'Updated notes' },
  source: 'manual'
});
```

## Testing Guide

### 1. Unit Testing Components

```typescript
// DiscDetailPage.test.tsx
import { render, screen } from '@testing-library/react';
import { DiscDetailPage } from '@/components/inventory/DiscDetailPage';
import { mockDisc } from '@/test/fixtures';

describe('DiscDetailPage', () => {
  it('renders disc information correctly', () => {
    render(<DiscDetailPage discId={mockDisc.id} />);
    
    expect(screen.getByText(mockDisc.manufacturer)).toBeInTheDocument();
    expect(screen.getByText(mockDisc.mold)).toBeInTheDocument();
  });

  it('handles edit mode toggle', async () => {
    render(<DiscDetailPage discId={mockDisc.id} />);
    
    const editButton = screen.getByRole('button', { name: /edit/i });
    await userEvent.click(editButton);
    
    expect(screen.getByText('Save Changes')).toBeInTheDocument();
  });
});
```

### 2. Testing Usage History

```typescript
// useUsageHistory.test.ts
import { renderHook, act } from '@testing-library/react';
import { useUsageHistory } from '@/hooks/useUsageHistory';

describe('useUsageHistory', () => {
  it('tracks events correctly', async () => {
    const { result } = renderHook(() => useUsageHistory());
    
    await act(async () => {
      const trackResult = await result.current.trackEvent(
        'test-disc-id',
        UsageEventType.VIEW
      );
      expect(trackResult.success).toBe(true);
    });
    
    const history = result.current.getDiscHistory('test-disc-id');
    expect(history).toHaveLength(1);
    expect(history[0].type).toBe(UsageEventType.VIEW);
  });
});
```

### 3. Testing Recommendations

```typescript
// discRecommendations.test.ts
import { generateDiscRecommendations } from '@/lib/discRecommendations';
import { mockDiscs } from '@/test/fixtures';

describe('discRecommendations', () => {
  it('generates relevant recommendations', () => {
    const [targetDisc, ...collection] = mockDiscs;
    
    const recommendations = generateDiscRecommendations(
      targetDisc,
      collection,
      mockDiscs
    );
    
    expect(recommendations).toHaveLength(6);
    expect(recommendations[0].score).toBeGreaterThan(0);
    expect(recommendations[0].reasons).toContain('similar_flight');
  });
});
```

### 4. Integration Testing

```typescript
// discDetail.integration.test.tsx
describe('Disc Detail Integration', () => {
  it('completes full edit workflow', async () => {
    render(<DiscDetailPage discId={mockDisc.id} />);
    
    // Start editing
    await userEvent.click(screen.getByRole('button', { name: /edit/i }));
    
    // Modify field
    const weightInput = screen.getByLabelText(/weight/i);
    await userEvent.clear(weightInput);
    await userEvent.type(weightInput, '173');
    
    // Save changes
    await userEvent.click(screen.getByRole('button', { name: /save/i }));
    
    // Verify update
    expect(screen.getByText('173g')).toBeInTheDocument();
  });
});
```

### 5. Accessibility Testing

```typescript
// accessibility.test.tsx
import { axe, toHaveNoViolations } from 'jest-axe';

expect.extend(toHaveNoViolations);

describe('Disc Detail Accessibility', () => {
  it('has no accessibility violations', async () => {
    const { container } = render(<DiscDetailPage discId={mockDisc.id} />);
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });
});
```

## Performance Testing

### 1. Load Testing

```typescript
// performance.test.ts
describe('Performance', () => {
  it('loads detail page within performance budget', async () => {
    const startTime = performance.now();
    
    render(<DiscDetailPage discId={mockDisc.id} />);
    await waitFor(() => screen.getByText(mockDisc.manufacturer));
    
    const loadTime = performance.now() - startTime;
    expect(loadTime).toBeLessThan(2000); // 2 second budget
  });
});
```

### 2. Memory Testing

```typescript
// memory.test.ts
describe('Memory Usage', () => {
  it('cleans up usage history properly', () => {
    const { unmount } = render(<DiscDetailPage discId={mockDisc.id} />);
    
    // Simulate usage
    // ... usage tracking operations
    
    unmount();
    
    // Verify cleanup
    expect(window.addEventListener).toHaveBeenCalledWith('beforeunload');
  });
});
```

## Error Handling Testing

### 1. Network Errors

```typescript
// errorHandling.test.tsx
describe('Error Handling', () => {
  it('handles disc not found gracefully', () => {
    render(<DiscDetailPage discId="non-existent-id" />);
    
    expect(screen.getByText(/disc not found/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /back to inventory/i })).toBeInTheDocument();
  });
});
```

### 2. Validation Errors

```typescript
// validation.test.tsx
describe('Form Validation', () => {
  it('shows validation errors for invalid input', async () => {
    render(<EditDiscForm disc={mockDisc} onSave={jest.fn()} onCancel={jest.fn()} />);
    
    const weightInput = screen.getByLabelText(/weight/i);
    await userEvent.clear(weightInput);
    await userEvent.type(weightInput, '999'); // Invalid weight
    
    expect(screen.getByText(/weight must be between/i)).toBeInTheDocument();
  });
});
```

## Best Practices

### 1. Component Testing

- Test component behavior, not implementation details
- Use realistic test data that matches production scenarios
- Test accessibility and keyboard navigation
- Verify error states and edge cases

### 2. Hook Testing

- Test hook logic in isolation
- Mock external dependencies appropriately
- Test async operations with proper waiting
- Verify cleanup and memory management

### 3. Integration Testing

- Test complete user workflows
- Verify data flow between components
- Test navigation and routing
- Validate form submissions and updates

### 4. Performance Testing

- Set performance budgets and test against them
- Monitor memory usage and cleanup
- Test with realistic data volumes
- Verify loading states and transitions

## Debugging Tips

### 1. Usage History Issues

```typescript
// Debug usage history
const { exportHistory } = useUsageHistory();
console.log('Usage History:', exportHistory());
```

### 2. Recommendation Issues

```typescript
// Debug recommendations
const recommendations = generateDiscRecommendations(disc, collection, allDiscs);
console.log('Recommendations:', recommendations.map(r => ({
  disc: r.disc.mold,
  score: r.score,
  reasons: r.reasons
})));
```

### 3. Form Validation Issues

```typescript
// Debug form state
const { formState } = useForm();
console.log('Form Errors:', formState.errors);
console.log('Form Values:', watch());
```

## Maintenance Notes

### 1. Regular Tasks

- Monitor usage history storage size
- Update recommendation algorithms based on user feedback
- Review and update validation schemas
- Optimize performance based on usage patterns

### 2. Future Enhancements

- Add server-side rendering for better SEO
- Implement real-time collaboration features
- Add advanced analytics and insights
- Integrate with external disc databases

### 3. Breaking Changes

- Always provide migration paths for data changes
- Maintain backward compatibility for stored data
- Document breaking changes thoroughly
- Provide clear upgrade instructions

This guide provides comprehensive examples and testing strategies for the Disc Detail Pages implementation, ensuring maintainable and reliable code.
