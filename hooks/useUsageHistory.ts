/**
 * Usage History Hook for Disc Golf Inventory Management System
 *
 * This hook provides React integration for the usage history tracking system,
 * offering convenient methods to track disc usage events and retrieve analytics.
 *
 * Features:
 * - Automatic event tracking for common operations
 * - Real-time usage statistics
 * - Collection-wide analytics
 * - Error handling and loading states
 * - TypeScript type safety
 */

import { useCallback, useEffect, useState } from "react";
import {
  recordUsageEvent,
  getDiscUsageHistory,
  getRecentUsageEvents,
  calculateDiscUsageStats,
  calculateCollectionUsageAnalytics,
  clearUsageHistory,
  exportUsageHistory,
  UsageEventType,
  type UsageEvent,
  type UsageEventMetadata,
  type DiscUsageStats,
  type CollectionUsageAnalytics,
} from "@/lib/usageHistory";
import { StorageResult } from "@/lib/storage";

// ============================================================================
// TYPES
// ============================================================================

/**
 * Hook configuration options
 */
export interface UseUsageHistoryOptions {
  /** Whether to automatically track view events */
  autoTrackViews?: boolean;
  /** Debounce delay for view tracking in milliseconds */
  viewTrackingDelay?: number;
  /** Whether to track detailed metadata */
  trackMetadata?: boolean;
}

/**
 * Hook return type
 */
export interface UseUsageHistoryReturn {
  // Event tracking
  trackEvent: (
    discId: string,
    type: UsageEventType,
    metadata?: UsageEventMetadata
  ) => Promise<StorageResult<UsageEvent>>;
  trackView: (discId: string, metadata?: UsageEventMetadata) => Promise<StorageResult<UsageEvent>>;
  trackEdit: (discId: string, metadata?: UsageEventMetadata) => Promise<StorageResult<UsageEvent>>;
  trackLocationChange: (discId: string, oldLocation: string, newLocation: string) => Promise<StorageResult<UsageEvent>>;
  trackConditionChange: (
    discId: string,
    oldCondition: string,
    newCondition: string
  ) => Promise<StorageResult<UsageEvent>>;

  // Data retrieval
  getDiscHistory: (discId: string) => UsageEvent[];
  getDiscStats: (discId: string) => DiscUsageStats;
  getRecentEvents: (limit?: number) => UsageEvent[];
  getCollectionAnalytics: () => CollectionUsageAnalytics;

  // Utility functions
  clearHistory: () => Promise<StorageResult<void>>;
  exportHistory: () => string;

  // State
  loading: boolean;
  error: string | null;

  // View tracking methods
  startViewTracking: (discId: string) => void;
  stopViewTracking: () => void;
}

// ============================================================================
// HOOK IMPLEMENTATION
// ============================================================================

/**
 * Custom hook for usage history tracking and analytics
 *
 * @param options - Hook configuration options
 * @returns Hook state and methods
 */
export function useUsageHistory(options: UseUsageHistoryOptions = {}): UseUsageHistoryReturn {
  const { autoTrackViews = true, viewTrackingDelay = 1000, trackMetadata = true } = options;

  // State
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // View tracking state
  const [viewStartTime, setViewStartTime] = useState<number | null>(null);
  const [currentDiscId, setCurrentDiscId] = useState<string | null>(null);

  // ============================================================================
  // EVENT TRACKING FUNCTIONS
  // ============================================================================

  /**
   * Track a usage event
   */
  const trackEvent = useCallback(
    async (discId: string, type: UsageEventType, metadata?: UsageEventMetadata): Promise<StorageResult<UsageEvent>> => {
      try {
        setLoading(true);
        setError(null);

        const enhancedMetadata = trackMetadata
          ? {
              ...metadata,
              referrer: typeof window !== "undefined" ? document.referrer : undefined,
            }
          : metadata;

        const result = recordUsageEvent(discId, type, enhancedMetadata);

        if (!result.success) {
          setError(result.error?.message || "Failed to track event");
        }

        return result;
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : "Unknown error occurred";
        setError(errorMessage);
        return {
          success: false,
          error: {
            type: "OPERATION_FAILED" as const,
            message: errorMessage,
            details: err,
          },
        };
      } finally {
        setLoading(false);
      }
    },
    [trackMetadata]
  );

  /**
   * Track a view event with duration calculation
   */
  const trackView = useCallback(
    async (discId: string, metadata?: UsageEventMetadata): Promise<StorageResult<UsageEvent>> => {
      const viewMetadata: UsageEventMetadata = {
        ...metadata,
        viewDuration: viewStartTime ? Date.now() - viewStartTime : undefined,
      };

      return trackEvent(discId, UsageEventType.VIEW, viewMetadata);
    },
    [trackEvent, viewStartTime]
  );

  /**
   * Track an edit event
   */
  const trackEdit = useCallback(
    async (discId: string, metadata?: UsageEventMetadata): Promise<StorageResult<UsageEvent>> => {
      return trackEvent(discId, UsageEventType.EDIT, metadata);
    },
    [trackEvent]
  );

  /**
   * Track a location change event
   */
  const trackLocationChange = useCallback(
    async (discId: string, oldLocation: string, newLocation: string): Promise<StorageResult<UsageEvent>> => {
      return trackEvent(discId, UsageEventType.LOCATION_CHANGE, {
        oldLocation: oldLocation as any,
        newLocation: newLocation as any,
      });
    },
    [trackEvent]
  );

  /**
   * Track a condition change event
   */
  const trackConditionChange = useCallback(
    async (discId: string, oldCondition: string, newCondition: string): Promise<StorageResult<UsageEvent>> => {
      return trackEvent(discId, UsageEventType.CONDITION_CHANGE, {
        oldCondition: oldCondition as any,
        newCondition: newCondition as any,
      });
    },
    [trackEvent]
  );

  // ============================================================================
  // DATA RETRIEVAL FUNCTIONS
  // ============================================================================

  /**
   * Get usage history for a specific disc
   */
  const getDiscHistory = useCallback((discId: string): UsageEvent[] => {
    try {
      return getDiscUsageHistory(discId);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to get disc history");
      return [];
    }
  }, []);

  /**
   * Get usage statistics for a specific disc
   */
  const getDiscStats = useCallback((discId: string): DiscUsageStats => {
    try {
      return calculateDiscUsageStats(discId);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to calculate disc stats");
      return {
        discId,
        totalViews: 0,
        totalEdits: 0,
        createdAt: new Date(),
        locationChanges: 0,
        conditionChanges: 0,
        editFrequency: 0,
      };
    }
  }, []);

  /**
   * Get recent usage events
   */
  const getRecentEvents = useCallback((limit: number = 50): UsageEvent[] => {
    try {
      return getRecentUsageEvents(limit);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to get recent events");
      return [];
    }
  }, []);

  /**
   * Get collection-wide analytics
   */
  const getCollectionAnalytics = useCallback((): CollectionUsageAnalytics => {
    try {
      return calculateCollectionUsageAnalytics();
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to calculate analytics");
      return {
        totalEvents: 0,
        totalViews: 0,
        totalEdits: 0,
        mostViewedDiscs: [],
        mostEditedDiscs: [],
        recentActivity: [],
        dailyActivity: {},
        eventTypeDistribution: {
          [UsageEventType.VIEW]: 0,
          [UsageEventType.EDIT]: 0,
          [UsageEventType.LOCATION_CHANGE]: 0,
          [UsageEventType.CONDITION_CHANGE]: 0,
          [UsageEventType.NOTE_UPDATE]: 0,
          [UsageEventType.IMAGE_UPDATE]: 0,
          [UsageEventType.CREATED]: 0,
          [UsageEventType.DELETED]: 0,
        },
      };
    }
  }, []);

  // ============================================================================
  // UTILITY FUNCTIONS
  // ============================================================================

  /**
   * Clear all usage history
   */
  const clearHistory = useCallback(async (): Promise<StorageResult<void>> => {
    try {
      setLoading(true);
      setError(null);

      const result = clearUsageHistory();

      if (!result.success) {
        setError(result.error?.message || "Failed to clear history");
      }

      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Unknown error occurred";
      setError(errorMessage);
      return {
        success: false,
        error: {
          type: "OPERATION_FAILED" as const,
          message: errorMessage,
          details: err,
        },
      };
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Export usage history as JSON
   */
  const exportHistory = useCallback((): string => {
    try {
      return exportUsageHistory();
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to export history");
      return "[]";
    }
  }, []);

  // ============================================================================
  // AUTOMATIC VIEW TRACKING
  // ============================================================================

  /**
   * Start tracking view time for a disc
   */
  const startViewTracking = useCallback(
    (discId: string) => {
      if (autoTrackViews) {
        setViewStartTime(Date.now());
        setCurrentDiscId(discId);
      }
    },
    [autoTrackViews]
  );

  /**
   * Stop tracking view time and record the event
   */
  const stopViewTracking = useCallback(() => {
    if (autoTrackViews && currentDiscId && viewStartTime) {
      const viewDuration = Date.now() - viewStartTime;

      // Only track if view was longer than the delay threshold
      if (viewDuration >= viewTrackingDelay) {
        trackView(currentDiscId, { viewDuration });
      }

      setViewStartTime(null);
      setCurrentDiscId(null);
    }
  }, [autoTrackViews, currentDiscId, viewStartTime, viewTrackingDelay, trackView]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stopViewTracking();
    };
  }, [stopViewTracking]);

  return {
    // Event tracking
    trackEvent,
    trackView,
    trackEdit,
    trackLocationChange,
    trackConditionChange,

    // Data retrieval
    getDiscHistory,
    getDiscStats,
    getRecentEvents,
    getCollectionAnalytics,

    // Utility functions
    clearHistory,
    exportHistory,

    // State
    loading,
    error,

    // View tracking methods
    startViewTracking,
    stopViewTracking,
  };
}
